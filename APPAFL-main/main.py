# 这个是改进版的AsyFL方案，没有使用加权更新，增加了落后客户端的本地迭代轮次(即将改为增加学习率)，也添加了mu项，
# 使用服务器保存的陈旧模型更新，也进行了量化，并且延迟客户端的选择上，不是每轮都有四个八个客户端延迟而改为
# 最多有四个或八个等等个延迟，不聚合延迟轮次大于threshold的延迟模型，但还没有添加掩码
import os
import argparse
from Crypto.Util import number
from tqdm import tqdm
import random
import numpy as np
import torch
import torch.nn.functional as F
from torch import optim
from Models import Mnist_2NN, Mnist_CNN, Cifar_CNN, CNNCifar
from clients import ClientsGroup, client
import os
# from Encryption import encryption  # 加密模块（如果存在的话）

# 创建命令行参数解析器
parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter, description="FedAvg")
# GPU设置参数
parser.add_argument('-g', '--gpu', type=str, default='0', help='gpu id to use(e.g. 0,1,2,3)')
# 客户端数量参数
parser.add_argument('-nc', '--num_of_clients', type=int, default=20, help='numer of the clients')
# 客户端参与比例参数
parser.add_argument('-cf', '--cfraction', type=float, default=1,
                    help='C fraction, 0 means 1 client, 1 means total clients')
# 本地训练轮数参数
parser.add_argument('-E', '--epoch', type=int, default=5, help='local train epoch')
# 本地训练批次大小参数
parser.add_argument('-B', '--batchsize', type=int, default=10, help='local train batch size')
# 模型名称参数
parser.add_argument('-mn', '--model_name', type=str, default='CNNCifar', help='the model to train')
# 学习率参数
parser.add_argument('-lr', "--learning_rate", type=float, default=0.01, help="learning rate, \
                    use value from origin paper as default")
# 验证频率参数
parser.add_argument('-vf', "--val_freq", type=int, default=1, help="model validation frequency(of communications)")
# 模型保存频率参数
parser.add_argument('-sf', '--save_freq', type=int, default=1, help='global model save frequency(of communication)')
# 通信轮数参数
parser.add_argument('-ncomm', '--num_comm', type=int, default=100, help='number of communications')
# 模型保存路径参数
parser.add_argument('-sp', '--save_path', type=str, default='./checkpoints', help='the saving path of checkpoints')
# 数据分布类型参数（0为Non-IID，1为IID）
parser.add_argument('-iid', '--IID', type=int, default=0, help='the way to allocate data to clients')  # 0是noniid,1是iid
# FedProx正则化参数
parser.add_argument('--mu', type=float, default=0.01, help='proximal term constant')
# 量化位宽参数
parser.add_argument('--q_width', type=int, default=16)


def tes_mkdir(path):
    """
    创建目录的辅助函数
    Args:
        path: 要创建的目录路径
    """
    if not os.path.isdir(path):
        os.mkdir(path)


# 同态加密参数设置
k0 = 2048                    # 第一个安全参数
k1 = 20                      # 第二个安全参数
k2 = 160                     # 第三个安全参数
p = number.getPrime(k0)      # 生成k0位的素数p
q = number.getPrime(k0)      # 生成k0位的素数q
N = p * q                    # 计算RSA模数N
# 密钥对：私钥sk=(p,L)，公钥pk=(k0,k1,k2,N)
L = number.getPrime(k2)      # 生成k2位的素数L


def she_enc(p, L, m):
    """
    同态加密函数
    Args:
        p: 素数p（私钥的一部分）
        L: 素数L（私钥的一部分）
        m: 明文值
    Returns:
        int: 加密后的密文值
    """
    r = random.getrandbits(k2)   # 生成k2位随机数r
    r1 = random.getrandbits(k0)  # 生成k0位随机数r1
    return ((r * L + m) * (1 + r1 * p)) % N


def she_dec(p, L, c):
    """
    同态解密函数
    Args:
        p: 素数p（私钥的一部分）
        L: 素数L（私钥的一部分）
        c: 密文值
    Returns:
        int: 解密后的明文值
    """
    m = (c % p) % L
    if m < L / 2:
        return m
    else:
        return m - L


# def StaleClientDecide(num_in_comm, PercentageOfStale):
#     """
#     延迟客户端选择函数（已废弃）
#     这个函数用来在num_in_comm个客户端中选择百分之PercentageOfStale个客户端作为掉线的客户端
#     """
#     clients_in_comm = ['client{}'.format(i) for i in range(num_in_comm)]  # 生成num_in_comm个客户端
#     index = [i for i in order[0:num_in_comm][num_in_comm*PercentageOfStale]]  # 随机生成指定数目延迟客户端的索引
#     return clients_in_comm, index

def flagUpdate(flag, time_stamp, epoch, percentageOfStale):
    """
    更新客户端参与标志的函数
    Args:
        flag: 客户端参与标志列表（0表示延迟，1表示正常）
        time_stamp: 客户端时间戳列表
        epoch: 当前训练轮次
        percentageOfStale: 延迟客户端的百分比
    Returns:
        tuple: 更新后的flag和time_stamp
    """
    num = flag.count(0)  # 统计当前轮次中有几个延迟客户端
    if num == 0:
        # 随机生成指定数目延迟客户端的索引
        index = np.random.permutation(num_in_comm)[0:int(num_in_comm * percentageOfStale - num)]
        # print(index)
        # 对于上一轮没有延迟的客户，将延迟标志设为0，时间戳设为上一轮次
        for j in range(len(index)):
            if flag[index[j]] != 0:
                flag[index[j]] = 0          # 设置为延迟状态
                time_stamp[index[j]] = epoch # 记录开始延迟的轮次
    return flag, time_stamp


def stale_timeUpdate(flag, stale_time, stale_threshold):
    """
    更新客户端延迟时间的函数
    Args:
        flag: 客户端参与标志列表
        stale_time: 客户端延迟时间列表
        stale_threshold: 延迟阈值
    Returns:
        list: 更新后的stale_time
    """
    index = [i for i, x in enumerate(flag) if x == 0]  # 获取延迟客户端的索引
    for j in range(len(index)):
        # print(j)
        # 如果该客户端上一轮没有延迟则设置延迟轮数，如果上一轮延迟则不做更改
        if stale_time[index[j]] == 0:
            # print(index[j])
            # 随机确定客户端延迟的轮数（1到stale_threshold之间）
            stale_time[index[j]] = np.random.permutation(stale_threshold)[0] + 1
            # print(stale_time[index[j]])
    return stale_time


# def quantize_per_layer(party, r_maxs, bit_width=16):
#     result = []
#     for component, r_max in zip(party, r_maxs):
#         x, _ = encryption.quantize_matrix_stochastic(component, bit_width=bit_width, r_max=r_max)
#         result.append(x)
#     return np.array(result)
#
#
# def unquantize_per_layer(party, r_maxs, bit_width=16):
#     result = []
#     for component, r_max in zip(party, r_maxs):
#         result.append(encryption.unquantize_matrix(component, bit_width=bit_width, r_max=r_max).astype(np.float32))
#     return np.array(result)


if __name__ == "__main__":
    """
    APPAFL主程序
    实现改进版的异步联邦学习方案，包含同态加密和隐私保护功能
    """
    # 解析命令行参数
    args = parser.parse_args()
    args = args.__dict__

    # 创建模型保存目录
    tes_mkdir(args['save_path'])

    # 设置GPU环境
    os.environ['CUDA_VISIBLE_DEVICES'] = args['gpu']
    dev = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    # 根据模型名称创建相应的神经网络模型
    net = None
    if args['model_name'] == 'mnist_2nn':
        net = Mnist_2NN()           # MNIST两层全连接网络
    elif args['model_name'] == 'mnist_cnn':
        net = Mnist_CNN()           # MNIST卷积神经网络
    elif args['model_name'] == 'cifar10_cnn':
        net = Cifar_CNN()           # CIFAR-10卷积神经网络
    elif args['model_name'] == 'CNNCifar':
        net = CNNCifar()            # CIFAR-10深度卷积神经网络

    # 如果有多个GPU，使用数据并行
    if torch.cuda.device_count() > 1:
        print("Let's use", torch.cuda.device_count(), "GPUs!")
        net = torch.nn.DataParallel(net)
    # 将模型移动到指定设备
    net = net.to(dev)

    # 定义损失函数
    loss_func = F.cross_entropy
    # opti = optim.SGD(net.parameters(), lr=args['learning_rate'])  # 优化器在客户端本地训练时创建

    # 创建客户端组（使用CIFAR-10数据集）
    myClients = ClientsGroup('cifar10', args['IID'], args['num_of_clients'], dev, args["mu"])
    testDataLoader = myClients.test_data_loader

    # 计算每轮通信中参与的客户端数量
    num_in_comm = int(max(args['num_of_clients'] * args['cfraction'], 1))

    AsyAccuracy = []  # 存放模型准确率

    # 初始化全局模型参数
    global_parameters = {}      # 最新的全局模型参数
    stale_global_parameters = {}  # 陈旧的全局模型参数（用于延迟客户端）
    for key, var in net.state_dict().items():
        global_parameters[key] = var.clone()
        stale_global_parameters[key] = var.clone()

    # 异步联邦学习相关参数设置
    client_nums = args['num_of_clients']
    percentageOfStale = 0.4     # 延迟客户端所占总客户端数目的百分比
    stale_threshold = 4         # 每个客户端延迟的阈值（最大延迟轮数）
    flag = [1] * client_nums            # 客户端参与训练标志，0代表此轮不参与训练，1代表此轮参与训练
    weight = [1] * client_nums          # 客户端在聚合时的权重
    stale_time = [0] * client_nums      # 客户端延时的轮次，0表示不延迟
    time_stamp = [0] * client_nums      # 时间戳，表示客户端参与的最近一次训练的全局轮次
    threshold = 6              # 陈旧模型聚合阈值，超过此阈值的延迟模型不参与聚合
    shape = []                 # 存储客户端每层模型参数的维度（用于加密后的重塑）

    # 开始联邦学习训练循环
    for i in range(args['num_comm']):
        print("communicate round {}".format(i + 1))
        number = 0  # 记录被排除聚合的客户端数量

        # 随机选择参与本轮训练的客户端
        order = np.random.permutation(args['num_of_clients'])
        clients_in_comm = ['client{}'.format(i) for i in order[0:num_in_comm]]

        sum_parameters = None  # 聚合后的模型参数

        u = []  # 存储没有参与训练但使用陈旧模型的客户端

        # 从第二轮开始处理客户端延迟逻辑
        if i != 0:
            # 更新客户端参与标志和延迟时间
            flag, time_stamp = flagUpdate(flag, time_stamp, i, percentageOfStale)
            stale_time = stale_timeUpdate(flag, stale_time, stale_threshold)
            print("客户端的延迟轮次为：{}".format(stale_time))

            # 处理正常客户端（flag=1）
            id = [key for key, value in enumerate(flag) if value == 1]  # 获取正常客户端的索引
            for m in range(len(id)):
                weight[id[m]] = 1  # 设置正常客户端权重为1
                # print('客户{}的权重分数为{}'.format(id[m], weight[id[m]]))

            # 处理延迟客户端（flag=0）
            iid = [key for key, value in enumerate(flag) if value == 0]  # 获取延迟客户端的索引
            s = []  # 存放本轮结束延迟、重新参与训练的客户端索引

            for m in range(len(iid)):
                stale_time[iid[m]] -= 1  # 延迟时间减1
                # print(stale_time[iid[m]])

                # 如果延迟时间结束，客户端重新参与训练
                if stale_time[iid[m]] == 0:
                    s.append(iid[m])
                    flag[iid[m]] = 1  # 恢复参与状态

                    # 根据延迟轮数设置权重
                    # weight[iid[m]] = (i + 1) / (((i+1) - time_stamp[iid[m]])*args['num_comm'])  # 旧的权重计算方法
                    # weight[iid[m]] = ((i + 1)/args['num_comm']) * (1 - ((i+1) - time_stamp[iid[m]])/(i + 1)) # 旧的权重计算方法

                    # 如果延迟轮数超过阈值，则不参与聚合
                    if (i + 1) - time_stamp[iid[m]] > threshold:
                        weight[iid[m]] = 0
                        number += 1
                    else:
                        # 使用指数衰减权重：权重 = 2^(-延迟轮数)
                        weight[iid[m]] = 2**(-((i+1)-time_stamp[iid[m]]))

                    print('客户{}的权重分数为{}'.format(iid[m], weight[iid[m]]))

            print("参与此轮训练的延迟客户端：{}".format(s))
            print("未参与此轮训练的延迟客户端：{}".format(list(set(iid) - set(s))))

            # 处理未参与训练但可以使用陈旧模型的客户端
            if i < 100:  # 在前100轮中允许使用陈旧模型
                uu = list(set(iid) - set(s))  # 仍在延迟中的客户端
                for m in range(len(uu)):
                    # 如果延迟轮数不超过阈值，使用该客户端的陈旧模型
                    if (i + 1) - time_stamp[uu[m]] <= threshold:
                        weight[uu[m]] = 2 ** (-((i + 1) - time_stamp[uu[m]]))  # 指数衰减权重
                        u.append(uu[m])
                        print("{}的权重分数为{}(此轮未到达但是用该客户端的陈旧模型)".format(uu[m], weight[uu[m]]))
                    else:
                        print("此轮未使用客户端{}的陈旧模型参与聚合".format(uu[m]))
            else:
                u = []  # 100轮后不再使用陈旧模型
        # 获取参与此轮聚合的所有客户端索引（包括正常训练的和使用陈旧模型的）
        iiiid = [key for key, value in enumerate(flag) if value == 1]  # 正常参与训练的客户端
        iiid = iiiid + u  # 加上使用陈旧模型的客户端
        print("参与此轮训练的所有客户端：{}".format(iiiid))

        # 打印客户端时间戳信息（用于调试）
        time = []
        for b in range(client_nums):
            time.append(time_stamp[b])
        print("客户端的时间戳：{}".format(time))

        # 计算所有参与聚合的客户端权重总和
        sum_weight = 0
        for m in range(len(iiid)):
            sum_weight = sum_weight + weight[iiid[m]]

        rmax = []  # 量化相关参数（暂未使用）
        k = []     # 临时变量（暂未使用）

        # 遍历所有参与聚合的客户端进行训练和加密
        for m in range(len(iiid)):
            print('******{}******'.format(iiid[m]))
            local_gradients = []  # 本地梯度（暂未使用）

            # 判断客户端是否使用最新的全局模型还是陈旧模型
            if i - time_stamp[iiid[m]] == 1 or i == 0:
                # 对于连续参与训练的客户端，使用最新的全局模型
                opti = optim.SGD(net.parameters(), lr=args['learning_rate'])
                local_parameters = myClients.clients_set[clients_in_comm[iiid[m]]].localUpdate(
                    args['epoch'], args['batchsize'], net, loss_func, opti,
                    global_parameters, args["mu"])
            else:
                # 对于延迟后重新参与的客户端，使用其最后一次参与时的全局模型
                opti = optim.SGD(net.parameters(), lr=0.02)  # 使用稍高的学习率补偿延迟
                # 加载客户端最后一次参与训练时对应的全局模型
                stale_global_parameters = torch.load(os.path.join(args['save_path'],
                                                                  '{}_num_comm{}'.format(args['model_name'],
                                                                                         time_stamp[iiid[m]] - 1)))
                local_parameters = myClients.clients_set[clients_in_comm[iiid[m]]].localUpdate(
                    args['epoch'], args['batchsize'], net, loss_func, opti,
                    stale_global_parameters, args["mu"])

            # 模型参数量化处理
            for key, var in local_parameters.items():
                # print(key)
                # 将浮点参数量化为整数（乘以10^7后四舍五入）
                local_parameters[key] = np.rint(local_parameters[key].numpy() * 10000000).astype(int)
            # print(type(local_parameters['fc3.bias'][0]))
            # print(local_parameters['fc3.bias'])

            # 同态加密处理
            print("Encrypt.....")
            for key, var in local_parameters.items():
                # print(local_parameters[key].shape)
                # 在第一轮第一个客户端时保存参数维度信息
                if i == 0 and m == 0:
                    shape.append(np.array(local_parameters[key].shape))  # 保存当前层参数的维度
                # print(shape)

                # 将参数展平为一维列表以便逐个加密
                local_parameters[key] = np.array(local_parameters[key]).flatten().tolist()

                # 对每个参数值进行同态加密
                for j in range(len(local_parameters[key])):
                    local_parameters[key][j] = she_enc(p, L, local_parameters[key][j])  # 逐个加密
                # local_parameters[key] = np.reshape(local_parameters[key], np.array(a))  # 恢复初始维度（已注释）
                # print(np.array(local_parameters[key]).shape)


            # 加权聚合（注意：聚合时必须逐个元素相乘，不能直接用数组乘法）
            print("Aggregate....")
            if sum_parameters is None:
                # 初始化聚合参数字典
                sum_parameters = {}
                for key, var in local_parameters.items():
                    gset = []
                    # 对每个参数值进行加权
                    for r in range(len(local_parameters[key])):
                        # 计算加权值：参数值 × (客户端权重/总权重) × 1000（放大因子）
                        gset.append(var[r] * round((weight[iiid[m]] / sum_weight) * 1000))
                    sum_parameters[key] = gset
            else:
                # 累加其他客户端的加权参数
                for key, var in local_parameters.items():  # var是字典的属性值
                    for r in range(len(local_parameters[key])):
                        # 累加加权参数值
                        sum_parameters[key][r] = sum_parameters[key][r] + var[r] * round(
                                    (weight[iiid[m]] / sum_weight) * 1000)

        # 更新参与本轮训练的客户端时间戳
        for m in range(len(iiiid)):
            time_stamp[iiiid[m]] = i + 1  # 设置本轮到达的客户端的时间戳为当前训练轮次

        # 同态解密
        print("Decrypt....")
        for key, var in sum_parameters.items():
            print(key)
            # 对每个聚合后的参数值进行解密
            for r in range(len(sum_parameters[key])):
                sum_parameters[key][r] = she_dec(p, L, sum_parameters[key][r])  # 逐个解密

        # 反量化和维度重塑
        # 以下是使用专门量化函数的代码（已注释）
        # sum_gradients = []
        # for key, var in sum_parameters.items():
        #     sum_gradients.append(var.numpy())
        # sum_gradients = unquantize_per_layer(sum_gradients, rmax, bit_width=args['q_width'])
        # print(len(sum_gradients))

        idex = 0
        for key, var in sum_parameters.items():
            print(idex)
            print(shape[idex])
            # 反量化：除以10^10（因为聚合时乘了1000，量化时乘了10^7）
            for r in range(len(sum_parameters[key])):
                sum_parameters[key][r] = sum_parameters[key][r]/10000000000  # 逐个反量化
            # 恢复参数的原始维度
            sum_parameters[key] = np.reshape(sum_parameters[key], shape[idex])
            idex += 1

        # 将numpy数组转换为PyTorch张量并更新全局参数
        # for ith in range(len(sum_gradients)):
        #     sum_parameters[k[ith]] = torch.from_numpy(sum_gradients[ith])
        for var in global_parameters:
            global_parameters[var] = torch.tensor(sum_parameters[var])

        # 其他聚合方式（已注释）
        # for var in global_parameters:
        #     # global_parameters[var] = (sum_parameters[var] / num_in_comm)
        #     global_parameters[var] = sum_parameters[var] / (len(iiid) + number) + global_parameters[var]

        # 模型验证
        with torch.no_grad():
            if (i + 1) % args['val_freq'] == 0:
                # 加载聚合后的全局模型参数
                net.load_state_dict(global_parameters, strict=True)
                sum_accu = 0
                num = 0
                # 在测试集上评估模型性能
                for data, label in testDataLoader:
                    data, label = data.to(dev), label.to(dev)
                    preds = net(data)                           # 前向传播
                    preds = torch.argmax(preds, dim=1)          # 获取预测类别
                    sum_accu += (preds == label).float().mean() # 计算准确率
                    num += 1
                print('accuracy: {}'.format(sum_accu / num))
                AsyAccuracy.append(sum_accu / num)

        # 模型保存
        if (i + 1) % args['save_freq'] == 0:
            # 保存模型状态字典
            torch.save(net.state_dict(), os.path.join(args['save_path'],
                                                      '{}_num_comm{}'.format(args['model_name'], i)))
            # 完整模型保存（已注释）
            # torch.save(net, os.path.join(args['save_path'],
            #                              '{}_num_comm{}_E{}_B{}_lr{}_num_clients{}_cf{}'.format(args['model_name'],
            #                                                                                     i, args['epoch'],
            #                                                                                     args['batchsize'],
            #                                                                                     args['learning_rate'],
            #                                                                                     args['num_of_clients'],
            #                                                                                     args['cfraction'])))

        # 保存实验结果到文件
        r = os.getcwd() + "\\nonIID\SHE\Cifar10"
        np.savetxt(
            r + '\cnn2_FedProx_AsyAccuracy_Pro(staleThreshold={},percent={},mu={},threshold={},100stale,notAggUnderThre,5local,newflag).txt'.format(
                stale_threshold, percentageOfStale, args["mu"], threshold), AsyAccuracy)
