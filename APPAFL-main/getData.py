# 导入必要的库
import numpy as np
import gzip
import os
import platform
import pickle
import torchvision
import torch
from torchvision import transforms as transforms


class GetDataSet(object):
    """
    数据集获取和处理类
    支持MNIST和CIFAR-10数据集的加载和预处理
    """
    def __init__(self, dataSetName, isIID):
        """
        初始化数据集
        Args:
            dataSetName: 数据集名称（'mnist'或'cifar10'）
            isIID: 是否为IID数据分布（True为IID，False为Non-IID）
        """
        self.name = dataSetName              # 数据集名称
        self.train_data = None               # 训练数据
        self.train_label = None              # 训练标签
        self.train_data_size = None          # 训练数据大小
        self.test_data = None                # 测试数据
        self.test_label = None               # 测试标签
        self.test_data_size = None           # 测试数据大小

        self._index_in_train_epoch = 0       # 训练轮次中的索引

        # 根据数据集名称选择相应的构建方法
        if self.name == 'mnist':
            self.mnistDataSetConstruct(isIID)
        elif self.name == 'cifar10':
            self.load_data(isIID)
        else:
            pass


    def mnistDataSetConstruct(self, isIID):
        """
        构建MNIST数据集
        Args:
            isIID: 是否为IID数据分布
        """
        # MNIST数据集路径（需要根据实际路径修改）
        data_dir = r'E:\代码\FedAvg-master(1)\FedAvg-master\data\MNIST'
        # data_dir = r'./data/MNIST'

        # 构建各个数据文件的完整路径
        train_images_path = os.path.join(data_dir, 'train-images-idx3-ubyte.gz')
        train_labels_path = os.path.join(data_dir, 'train-labels-idx1-ubyte.gz')
        test_images_path = os.path.join(data_dir, 't10k-images-idx3-ubyte.gz')
        test_labels_path = os.path.join(data_dir, 't10k-labels-idx1-ubyte.gz')

        # 提取图像和标签数据
        train_images = extract_images(train_images_path)
        train_labels = extract_labels(train_labels_path)
        test_images = extract_images(test_images_path)
        test_labels = extract_labels(test_labels_path)
        # print(test_labels)

        # 验证数据和标签的数量一致性
        assert train_images.shape[0] == train_labels.shape[0]
        assert test_images.shape[0] == test_labels.shape[0]

        # 记录数据集大小
        self.train_data_size = train_images.shape[0]
        self.test_data_size = test_images.shape[0]

        # 验证图像通道数为1（灰度图像）
        assert train_images.shape[3] == 1
        assert test_images.shape[3] == 1

        # 将图像从4D重塑为2D（展平像素）
        train_images = train_images.reshape(train_images.shape[0], train_images.shape[1] * train_images.shape[2])
        test_images = test_images.reshape(test_images.shape[0], test_images.shape[1] * test_images.shape[2])
        # print(train_images.shape)

        # 数据类型转换和归一化
        train_images = train_images.astype(np.float32)
        train_images = np.multiply(train_images, 1.0 / 255.0)  # 归一化到[0,1]
        test_images = test_images.astype(np.float32)
        test_images = np.multiply(test_images, 1.0 / 255.0)    # 归一化到[0,1]

        # 根据IID设置决定数据分布方式
        if isIID:
            # IID分布：随机打乱数据
            order = np.arange(self.train_data_size)
            np.random.shuffle(order)
            self.train_data = train_images[order]
            self.train_label = train_labels[order]
        else:
            # Non-IID分布：按标签排序数据
            # print(train_labels)
            labels = np.argmax(train_labels, axis=1)  # 将one-hot编码转换为类别索引
            # print(labels)
            order = np.argsort(labels)                # 按标签排序
            self.train_data = train_images[order]
            self.train_label = train_labels[order]

        # 设置测试数据
        self.test_data = test_images
        self.test_label = test_labels

    def load_data(self, isIID):
        """
        加载CIFAR-10数据集
        Args:
            isIID: 是否为IID数据分布
        """
        # 定义数据变换
        train_transform = transforms.Compose([transforms.RandomHorizontalFlip(), transforms.ToTensor()])
        test_transform = transforms.Compose([transforms.ToTensor()])

        # 下载并加载CIFAR-10数据集
        train_set = torchvision.datasets.CIFAR10(root='./data/cifar10', train=True, download=True,
                                                 transform=train_transform)
        test_set = torchvision.datasets.CIFAR10(root='./data/cifar10', train=False, download=True,
                                                transform=test_transform)

        #print('cifar10')
        # 调整数据维度：从(N,H,W,C)转换为(N,C,H,W)
        train_data = train_set.data.transpose((0,3,1,2))  # (50000, 32, 32, 3) -> (50000, 3, 32, 32)
        print(train_data.shape)

        # 获取训练标签并转换为numpy数组
        train_labels = train_set.targets
        train_labels = np.array(train_labels)  # 将标签转化为numpy数组
        # train_labels = dense_to_one_hot(train_labels)  # 可选：转换为one-hot编码
        # print(type(train_labels))  # <class 'numpy.ndarray'>
        # print(train_labels.shape)  # (50000,)

        # 处理测试数据
        test_data = test_set.data.transpose((0,3,1,2))  # 测试数据维度调整
        test_labels = test_set.targets
        test_labels = np.array(test_labels)
        # test_labels = dense_to_one_hot(test_labels)  # 可选：转换为one-hot编码

        # 调试信息（已注释）
        # print(test_labels)
        # test_labels = tensor.index_select(0, test_labels)
        # y_one_hot = torch.zeros(len(test_labels), 10).scatter_(1, test_labels, 1)
        # print(y_one_hot)
        # print(test_labels)
        # print()

        # 可选的维度转换（已注释）
        # train_data = train_data.transpose((0, 2, 3, 1))  # convert to HWC
        # test_data = test_data.transpose((0, 2, 3, 1))  # convert to HWC

        # 记录数据集大小
        self.train_data_size = train_data.shape[0]
        self.test_data_size = test_data.shape[0]

        # 可选的数据重塑操作（已注释）
        # print(train_data.shape)
        # 将训练集转化为（50000，32*32*3）矩阵
        # print(train_data.shape[1])
        # print(train_data.shape[3])
        # train_images = train_data.reshape(train_data.shape[0],
        #                                   train_data.shape[1], train_data.shape[2], train_data.shape[3])
        # # train_images = train_data.reshape(train_data.shape[0],
        # #                                   train_data.shape[1] * train_data.shape[2] * train_data.shape[3])
        # # print(train_images.shape)
        # # 将测试集转化为（10000，32*32*3）矩阵
        # test_images = test_data.reshape(test_data.shape[0],
        #                                 test_data.shape[1], test_data.shape[2],  test_data.shape[3])
        # # test_images = test_data.reshape(test_data.shape[0],
        # #                                 test_data.shape[1] * test_data.shape[2] * test_data.shape[3])

        # ---------------------------归一化处理------------------------------#
        # 数据类型转换和归一化
        train_images = train_data.astype(np.float32)
        # 数组对应元素位置相乘，归一化到[0,1]
        train_images = np.multiply(train_images, 1.0 / 255.0)
        # print(train_images[0:10,5:10])
        test_images = test_data.astype(np.float32)
        test_images = np.multiply(test_images, 1.0 / 255.0)
        # ----------------------------------------------------------------#
        # print(train_images)

        '''
        数据分布说明：
            总共有50000个训练样本
            假设有100个客户端
            IID分布：
                我们首先将数据集打乱，然后为每个Client分配500个样本。
            Non-IID分布：
                我们首先根据数据标签将数据集排序(即按类别排序)，
                然后将其划分为若干组数据切片，然后分给每个Client两个切片。
        '''
        if isIID:
            # IID分布：将50000个训练集随机打乱
            order = np.arange(self.train_data_size)
            print(self.train_data_size)
            print(order)
            np.random.shuffle(order)  # 随机打乱顺序
            self.train_data = train_images[order]
            self.train_label = train_labels[order]
            # print(self.train_data)
        else:
            # Non-IID分布：按照标签排序
            # labels = np.argmax(train_labels, axis=1)  # 如果是one-hot编码需要转换
            # 对数据标签进行排序
            order = np.argsort(train_labels)
            print(self.train_data_size)
            print(order)
            # print("标签下标排序")
            # print(train_labels[order[20000:25000]])
            self.train_data = train_images[order]
            self.train_label = train_labels[order]
            # print(self.train_data)
        # print(self.train_label)

        # 设置测试数据
        self.test_data = test_images
        self.test_label = test_labels


def _read32(bytestream):
    """
    从字节流中读取32位整数
    Args:
        bytestream: 字节流
    Returns:
        int: 读取的32位整数
    """
    dt = np.dtype(np.uint32).newbyteorder('>')  # 大端序32位无符号整数
    return np.frombuffer(bytestream.read(4), dtype=dt)[0]


def extract_images(filename):
    """
    从MNIST图像文件中提取图像数据
    Args:
        filename: 图像文件路径
    Returns:
        numpy.ndarray: 4D图像数组 [样本数, 高度, 宽度, 通道数]
    """
    print('Extracting', filename)
    with gzip.open(filename) as bytestream:
        # 读取魔数，验证文件格式
        magic = _read32(bytestream)
        if magic != 2051:
            raise ValueError(
                    'Invalid magic number %d in MNIST image file: %s' %
                    (magic, filename))
        # 读取图像数量、行数、列数
        num_images = _read32(bytestream)
        rows = _read32(bytestream)
        cols = _read32(bytestream)
        # 读取图像数据
        buf = bytestream.read(rows * cols * num_images)
        data = np.frombuffer(buf, dtype=np.uint8)
        # 重塑为4D数组：[样本数, 高度, 宽度, 通道数]
        data = data.reshape(num_images, rows, cols, 1)
        return data


def dense_to_one_hot(labels_dense, num_classes=10):
    """
    将稠密标签转换为one-hot编码
    Args:
        labels_dense: 稠密标签数组
        num_classes: 类别数量，默认为10
    Returns:
        numpy.ndarray: one-hot编码的标签数组
    """
    num_labels = labels_dense.shape[0]
    # 计算索引偏移
    index_offset = np.arange(num_labels) * num_classes
    # 创建零矩阵
    labels_one_hot = np.zeros((num_labels, num_classes))
    # 在相应位置设置为1
    labels_one_hot.flat[index_offset + labels_dense.ravel()] = 1
    return labels_one_hot


def extract_labels(filename):
    """
    从MNIST标签文件中提取标签数据
    Args:
        filename: 标签文件路径
    Returns:
        numpy.ndarray: one-hot编码的标签数组
    """
    print('Extracting', filename)
    with gzip.open(filename) as bytestream:
        # 读取魔数，验证文件格式
        magic = _read32(bytestream)
        if magic != 2049:
            raise ValueError(
                    'Invalid magic number %d in MNIST label file: %s' %
                    (magic, filename))
        # 读取标签数量
        num_items = _read32(bytestream)
        # 读取标签数据
        buf = bytestream.read(num_items)
        labels = np.frombuffer(buf, dtype=np.uint8)
        # print(labels)
        # 转换为one-hot编码
        return dense_to_one_hot(labels)


if __name__=="__main__":
    """
    测试数据集加载功能
    """
    print('test data set')
    # 测试CIFAR-10数据集加载（IID分布）
    mnistDataSet = GetDataSet('cifar10', True) # test NON-IID

    # 验证数据类型
    if type(mnistDataSet.train_data) is np.ndarray and type(mnistDataSet.test_data) is np.ndarray and \
            type(mnistDataSet.train_label) is np.ndarray and type(mnistDataSet.test_label) is np.ndarray:
        print('the type of data is numpy ndarray')
    else:
        print('the type of data is not numpy ndarray')

    # 打印数据集形状信息
    print('the shape of the train data set is {}'.format(mnistDataSet.train_data.shape))
    print('the shape of the test data set is {}'.format(mnistDataSet.test_data.shape))
    # 打印部分标签信息
    print(mnistDataSet.train_label[0:100], mnistDataSet.train_label[11000:11100])

