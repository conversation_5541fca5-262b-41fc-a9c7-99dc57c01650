# 导入必要的库
import numpy as np
import torch
from torch.utils.data import TensorDataset
from torch.utils.data import DataLoader
from getData import GetDataSet
import cv2


class client(object):
    """
    联邦学习客户端类
    每个客户端拥有自己的本地数据集，可以进行本地训练
    """
    def __init__(self, trainDataSet, dev):
        """
        初始化客户端
        Args:
            trainDataSet: 客户端的训练数据集
            dev: 计算设备（CPU或GPU）
        """
        self.train_ds = trainDataSet        # 训练数据集
        self.dev = dev                      # 计算设备
        self.train_dl = None                # 训练数据加载器
        self.local_parameters = None        # 本地模型参数
        self.dataset_size = 5000           # 数据集大小
        # self.isToPreprocess = preprocess  # 是否进行数据预处理（已注释）
        # print('init')
        # if self.isToPreprocess == 1:
        #     print("ispre")
        #     self.preprocess()

    def localUpdate(self, localEpoch, localBatchSize, Net, lossFun, opti, global_parameters, mu):
        """
        客户端本地训练函数
        Args:
            localEpoch: 本地训练轮数
            localBatchSize: 本地训练批次大小
            Net: 神经网络模型
            lossFun: 损失函数
            opti: 优化器
            global_parameters: 全局模型参数
            mu: 正则化参数（用于FedProx等算法）
        Returns:
            Net.state_dict(): 训练后的本地模型参数
        """
        # 加载全局模型参数到本地模型
        Net.load_state_dict(global_parameters, strict=True)
        # 创建数据加载器
        self.train_dl = DataLoader(self.train_ds, batch_size=localBatchSize, shuffle=True)

        # 进行本地训练
        for epoch in range(localEpoch):
            for data, label in self.train_dl:
                # 将数据和标签移动到指定设备
                data, label = data.to(self.dev), label.to(self.dev)
                # 前向传播
                preds = Net(data)
                # 计算损失
                loss = lossFun(preds, label)
                # 反向传播
                loss.backward()
                # 更新参数
                opti.step()
                # 清零梯度
                opti.zero_grad()

        # 返回训练后的模型参数
        return Net.state_dict()

    def preprocess(self):
        """
        数据预处理函数
        对图像数据进行随机裁剪、翻转和标准化等操作
        """
        print("preprocess")
        new_images = []
        shape = (24, 24, 3)  # 目标图像尺寸

        for i in range(self.dataset_size):
            # 获取原始图像
            old_image = self.train_ds[i, :, :, :]
            # 对图像进行填充
            old_image = np.pad(old_image, [[4, 4], [4, 4], [0, 0]], 'constant')
            # 随机裁剪
            left = np.random.randint(old_image.shape[0] - shape[0] + 1)
            top = np.random.randint(old_image.shape[1] - shape[1] + 1)
            new_image = old_image[left: left + shape[0], top: top + shape[1], :]

            # 随机水平翻转
            if np.random.random() < 0.5:
                new_image = cv2.flip(new_image, 1)

            # 标准化处理
            mean = np.mean(new_image)
            std = np.max([np.std(new_image),
                          1.0 / np.sqrt(self.train_ds.shape[1] * self.train_ds.shape[2] * self.train_ds.shape[3])])
            new_image = (new_image - mean) / std

            new_images.append(new_image)

        # 更新训练数据集
        self.train_ds = new_images

    def local_val(self):
        """
        本地验证函数（暂未实现）
        """
        pass


class ClientsGroup(object):
    """
    客户端组管理类
    负责创建和管理多个联邦学习客户端，分配数据集
    """
    def __init__(self, dataSetName, isIID, numOfClients, dev, mu):
        """
        初始化客户端组
        Args:
            dataSetName: 数据集名称（'mnist'或'cifar10'）
            isIID: 是否为IID数据分布（1为IID，0为Non-IID）
            numOfClients: 客户端数量
            dev: 计算设备（CPU或GPU）
            mu: 正则化参数
        """
        self.data_set_name = dataSetName    # 数据集名称
        self.is_iid = isIID                 # 数据分布类型
        self.num_of_clients = numOfClients  # 客户端数量
        self.dev = dev                      # 计算设备
        self.clients_set = {}               # 客户端集合字典

        self.test_data_loader = None        # 测试数据加载器

        # 执行数据集分配
        self.dataSetBalanceAllocation()

    def dataSetBalanceAllocation(self):
        """
        数据集平衡分配函数
        将训练数据分配给各个客户端，创建测试数据加载器
        """
        # print(self.data_set_name)
        # 获取数据集
        mnistDataSet = GetDataSet(self.data_set_name, self.is_iid)

        # 准备测试数据
        test_data = torch.tensor(mnistDataSet.test_data)
        # print(torch.tensor(mnistDataSet.test_label))
        if self.data_set_name == 'mnist':
            # MNIST数据集需要将one-hot编码转换为类别索引
            test_label = torch.argmax(torch.tensor(mnistDataSet.test_label), dim=1)
        else:
            # CIFAR-10数据集直接使用类别标签
            test_label = torch.tensor(mnistDataSet.test_label)
        # print(test_label)
        # 创建测试数据加载器
        self.test_data_loader = DataLoader(TensorDataset(test_data, test_label), batch_size=100, shuffle=False)

        # 获取训练数据和标签
        train_data = mnistDataSet.train_data
        train_label = mnistDataSet.train_label

        # 计算每个数据分片的大小（每个客户端分配2个分片）
        shard_size = mnistDataSet.train_data_size // self.num_of_clients // 2
        # 随机排列分片索引
        shards_id = np.random.permutation(mnistDataSet.train_data_size // shard_size)

        # 为每个客户端分配数据
        for i in range(self.num_of_clients):
            # 为第i个客户端选择两个分片
            shards_id1 = shards_id[i * 2]
            shards_id2 = shards_id[i * 2 + 1]

            # 获取第一个分片的数据和标签
            data_shards1 = train_data[shards_id1 * shard_size: shards_id1 * shard_size + shard_size]
            data_shards2 = train_data[shards_id2 * shard_size: shards_id2 * shard_size + shard_size]
            label_shards1 = train_label[shards_id1 * shard_size: shards_id1 * shard_size + shard_size]
            label_shards2 = train_label[shards_id2 * shard_size: shards_id2 * shard_size + shard_size]

            # 调试信息（已注释）
            # if i == 0:
            #     print(label_shards1)
            #     print(label_shards2)
            #     print(len(np.vstack((label_shards1, label_shards2))))
            #     print(np.vstack((label_shards1, label_shards2)))

            # 合并两个分片的数据
            local_data, local_label = np.vstack((data_shards1, data_shards2)), np.vstack((label_shards1, label_shards2))

            # if i == 0:
            #     print(local_label)

            # 根据数据集类型处理标签格式
            if self.data_set_name == 'mnist':
                # MNIST：将one-hot编码转换为类别索引
                local_label = np.argmax(local_label, axis=1)
            elif self.data_set_name == 'cifar10':
                # CIFAR-10：展平标签数组
                local_label = np.ndarray.flatten(local_label)

            # if i == 0:
            #     print(local_label)
            # print(torch.tensor(local_data))
            # print(len(torch.tensor(local_data)))

            # 确定是否需要预处理（CIFAR-10需要预处理）
            preprocess = 1 if self.data_set_name == 'cifar10' else 0
            # 创建客户端实例
            someone = client(TensorDataset(torch.tensor(local_data), torch.tensor(local_label)), self.dev)
            # 将客户端添加到客户端集合中
            self.clients_set['client{}'.format(i)] = someone

if __name__=="__main__":
    """
    测试代码：创建客户端组并打印部分数据
    """
    MyClients = ClientsGroup('mnist', True, 100, 1)
    print(MyClients.clients_set['client10'].train_ds[0:100])
    print(MyClients.clients_set['client11'].train_ds[400:500])


