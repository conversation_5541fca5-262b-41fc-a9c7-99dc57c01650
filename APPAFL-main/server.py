# 这是FedAvg（联邦平均）方案的实现
import os
import argparse
from tqdm import tqdm
import numpy as np
import torch
import torch.nn.functional as F
from torch import optim
from Models import Mnist_2NN, Mnist_CNN, Cifar_CNN, CNNCifar
from clients import ClientsGroup
# from clients import ClientsGroup, client

# 创建命令行参数解析器
parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter, description="FedAvg")
# GPU设置参数
parser.add_argument('-g', '--gpu', type=str, default='0', help='gpu id to use(e.g. 0,1,2,3)')
# 客户端数量参数
parser.add_argument('-nc', '--num_of_clients', type=int, default=20, help='numer of the clients')
# 客户端参与比例参数（C fraction）
parser.add_argument('-cf', '--cfraction', type=float, default=1, help='C fraction, 0 means 1 client, 1 means total clients')
# 本地训练轮数参数
parser.add_argument('-E', '--epoch', type=int, default=5, help='local train epoch')
# 本地训练批次大小参数
parser.add_argument('-B', '--batchsize', type=int, default=10, help='local train batch size')
# 模型名称参数
parser.add_argument('-mn', '--model_name', type=str, default='CNNCifar', help='the model to train')
# 学习率参数
parser.add_argument('-lr', "--learning_rate", type=float, default=0.01, help="learning rate, \
                    use value from origin paper as default")
# 验证频率参数
parser.add_argument('-vf', "--val_freq", type=int, default=1, help="model validation frequency(of communications)")
# 模型保存频率参数
parser.add_argument('-sf', '--save_freq', type=int, default=20, help='global model save frequency(of communication)')
# 通信轮数参数
parser.add_argument('-ncomm', '--num_comm', type=int, default=100, help='number of communications')
# 模型保存路径参数
parser.add_argument('-sp', '--save_path', type=str, default='./checkpoints', help='the saving path of checkpoints')
# 数据分布类型参数（0为Non-IID，1为IID）
parser.add_argument('-iid', '--IID', type=int, default=0, help='the way to allocate data to clients')


def tes_mkdir(path):
    """
    创建目录的辅助函数
    Args:
        path: 要创建的目录路径
    """
    if not os.path.isdir(path):
        os.mkdir(path)


if __name__=="__main__":
    """
    FedAvg算法主程序
    实现标准的联邦平均算法
    """
    # 解析命令行参数
    args = parser.parse_args()
    args = args.__dict__

    # 创建模型保存目录
    tes_mkdir(args['save_path'])

    # 设置GPU环境
    os.environ['CUDA_VISIBLE_DEVICES'] = args['gpu']
    dev = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    # 根据模型名称创建相应的神经网络模型
    net = None
    if args['model_name'] == 'mnist_2nn':
        net = Mnist_2NN()           # MNIST两层全连接网络
    elif args['model_name'] == 'mnist_cnn':
        net = Mnist_CNN()           # MNIST卷积神经网络
    elif args['model_name'] == 'cifar10_cnn':
        net = Cifar_CNN()           # CIFAR-10卷积神经网络
    elif args['model_name'] == 'CNNCifar':
        net = CNNCifar()            # CIFAR-10深度卷积神经网络

    # 如果有多个GPU，使用数据并行
    if torch.cuda.device_count() > 1:
        print("Let's use", torch.cuda.device_count(), "GPUs!")
        net = torch.nn.DataParallel(net)
    # 将模型移动到指定设备
    net = net.to(dev)

    # 定义损失函数和优化器
    loss_func = F.cross_entropy                                    # 交叉熵损失函数
    opti = optim.SGD(net.parameters(), lr=args['learning_rate'])   # SGD优化器

    # 创建客户端组（使用CIFAR-10数据集）
    myClients = ClientsGroup('cifar10', args['IID'], args['num_of_clients'], dev, 0)
    testDataLoader = myClients.test_data_loader

    # 计算每轮通信中参与的客户端数量
    num_in_comm = int(max(args['num_of_clients'] * args['cfraction'], 1))

    # 初始化全局模型参数
    global_parameters = {}
    for key, var in net.state_dict().items():
        global_parameters[key] = var.clone()

    # 存储准确率的列表
    Accuracy = []

    # 开始联邦学习训练循环
    for i in range(args['num_comm']):
        print("communicate round {}".format(i+1))

        # 随机选择参与本轮训练的客户端
        order = np.random.permutation(args['num_of_clients'])
        clients_in_comm = ['client{}'.format(i) for i in order[0:num_in_comm]]

        # 聚合客户端模型参数
        sum_parameters = None
        for client in tqdm(clients_in_comm):
            # 客户端本地训练
            local_parameters = myClients.clients_set[client].localUpdate(args['epoch'], args['batchsize'], net,
                                                                         loss_func, opti, global_parameters, 0)
            # 累加客户端参数
            if sum_parameters is None:
                sum_parameters = {}
                for key, var in local_parameters.items():
                    sum_parameters[key] = var.clone()
            else:
                for var in sum_parameters:
                    sum_parameters[var] = sum_parameters[var] + local_parameters[var]

        # 计算全局模型参数（平均聚合）
        for var in global_parameters:
            global_parameters[var] = (sum_parameters[var] / num_in_comm)

        # 模型验证
        with torch.no_grad():
            if (i + 1) % args['val_freq'] == 0:
                # 加载全局模型参数
                net.load_state_dict(global_parameters, strict=True)
                sum_accu = 0
                num = 0
                # 在测试集上评估模型性能
                for data, label in testDataLoader:
                    data, label = data.to(dev), label.to(dev)
                    preds = net(data)                           # 前向传播
                    preds = torch.argmax(preds, dim=1)          # 获取预测类别
                    sum_accu += (preds == label).float().mean() # 计算准确率
                    num += 1
                print('accuracy: {}'.format(sum_accu / num))
                Accuracy.append(sum_accu / num)

        # 模型保存（已注释）
        # if (i + 1) % args['save_freq'] == 0:
        #     torch.save(net, os.path.join(args['save_path'],
        #                                  '{}_num_comm{}_E{}_B{}_lr{}_num_clients{}_cf{}'.format(args['model_name'],
        #                                                                                         i, args['epoch'],
        #                                                                                         args['batchsize'],
        #                                                                                         args['learning_rate'],
        #                                                                                         args['num_of_clients'],
        #                                                                                         args['cfraction'])))

        # 保存准确率结果到文件
        np.savetxt('Cifar10-Accuracy-cnn2,nonIID.txt', Accuracy)
