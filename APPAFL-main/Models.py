# 导入PyTorch相关模块
import torch
import torch.nn as nn
import torch.nn.functional as F


class Mnist_2NN(nn.Module):
    """
    MNIST数据集的两层全连接神经网络模型
    用于手写数字识别任务
    """
    def __init__(self):
        super().__init__()
        # 第一层全连接层：输入784维（28*28像素），输出200维
        self.fc1 = nn.Linear(784, 200)
        # 第二层全连接层：输入200维，输出200维
        self.fc2 = nn.Linear(200, 200)
        # 第三层全连接层（输出层）：输入200维，输出10维（10个数字类别）
        self.fc3 = nn.Linear(200, 10)

    def forward(self, inputs):
        """
        前向传播函数
        Args:
            inputs: 输入数据，形状为(batch_size, 784)
        Returns:
            tensor: 输出预测结果，形状为(batch_size, 10)
        """
        # 第一层：全连接 + ReLU激活
        tensor = F.relu(self.fc1(inputs))
        # 第二层：全连接 + ReLU激活
        tensor = F.relu(self.fc2(tensor))
        # 第三层：全连接（输出层，不使用激活函数）
        tensor = self.fc3(tensor)
        return tensor


class Mnist_CNN(nn.Module):
    """
    MNIST数据集的卷积神经网络模型
    包含两个卷积层和两个全连接层
    """
    def __init__(self):
        super().__init__()
        # 第一个卷积层：输入1通道，输出32通道，卷积核5x5，步长1，填充2
        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=5, stride=1, padding=2)
        # 第一个池化层：2x2最大池化，步长2
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        # 第二个卷积层：输入32通道，输出64通道，卷积核5x5，步长1，填充2
        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=5, stride=1, padding=2)
        # 第二个池化层：2x2最大池化，步长2
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        # 第一个全连接层：输入7*7*64=3136维，输出512维
        self.fc1 = nn.Linear(7*7*64, 512)
        # 第二个全连接层（输出层）：输入512维，输出10维
        self.fc2 = nn.Linear(512, 10)

    def forward(self, inputs):
        """
        前向传播函数
        Args:
            inputs: 输入数据，形状为(batch_size, 784)
        Returns:
            tensor: 输出预测结果，形状为(batch_size, 10)
        """
        # 将输入重塑为28x28的图像格式
        tensor = inputs.view(-1, 1, 28, 28)
        # 第一个卷积层 + ReLU激活
        tensor = F.relu(self.conv1(tensor))
        # 第一个池化层
        tensor = self.pool1(tensor)
        # 第二个卷积层 + ReLU激活
        tensor = F.relu(self.conv2(tensor))
        # 第二个池化层
        tensor = self.pool2(tensor)
        # 展平特征图为一维向量
        tensor = tensor.view(-1, 7*7*64)
        # 第一个全连接层 + ReLU激活
        tensor = F.relu(self.fc1(tensor))
        # 第二个全连接层（输出层）
        tensor = self.fc2(tensor)
        return tensor


class Cifar_CNN(nn.Module):
    """
    CIFAR-10数据集的卷积神经网络模型
    经典的LeNet-5架构变体
    """
    def __init__(self):
        super().__init__()
        # 第一个卷积层：输入3通道（RGB），输出6通道，卷积核5x5
        self.conv1 = nn.Conv2d(3, 6, 5)
        # 池化层：2x2最大池化，步长2
        self.pool = nn.MaxPool2d(2, 2)
        # 第二个卷积层：输入6通道，输出16通道，卷积核5x5
        self.conv2 = nn.Conv2d(6, 16, 5)
        # 第一个全连接层：输入16*5*5=400维，输出120维
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        # 第二个全连接层：输入120维，输出84维
        self.fc2 = nn.Linear(120, 84)
        # 第三个全连接层（输出层）：输入84维，输出10维（10个类别）
        self.fc3 = nn.Linear(84, 10)

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            x: 输出预测结果，形状为(batch_size, 10)
        """
        # 第一个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv1(x)))
        # 第二个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv2(x)))
        # 展平特征图为一维向量
        x = x.view(-1, 16 * 5 * 5)
        # 第一个全连接层 + ReLU激活
        x = F.relu(self.fc1(x))
        # 第二个全连接层 + ReLU激活
        x = F.relu(self.fc2(x))
        # 第三个全连接层（输出层）
        x = self.fc3(x)
        return x


class CNNCifar(nn.Module):
    """
    CIFAR-10数据集的深度卷积神经网络模型
    包含三个卷积层和两个全连接层，使用Dropout防止过拟合
    """
    def __init__(self):
        super(CNNCifar,self).__init__()
        # 第一个卷积层：输入3通道（RGB），输出32通道，卷积核3x3，填充1
        self.conv1 = nn.Conv2d(3, 32, kernel_size=(3, 3), padding=1)
        # 第二个卷积层：输入32通道，输出64通道，卷积核3x3，填充1
        self.conv2 = nn.Conv2d(32, 64, kernel_size=(3, 3), padding=1)
        # 第三个卷积层：输入64通道，输出128通道，卷积核3x3，填充1
        self.conv3 = nn.Conv2d(64, 128, kernel_size=(3, 3), padding=1)
        # 池化层：2x2最大池化，步长2
        self.pool = nn.MaxPool2d(2,2)
        # 第一个全连接层：输入128*4*4=2048维，输出1028维
        self.fc1 = nn.Linear(128 * 4 * 4, 1028)
        # 第二个全连接层（输出层）：输入1028维，输出10维（10个类别）
        self.fc2 = nn.Linear(1028, 10)
        # Dropout层：丢弃率0.3，用于防止过拟合
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            x: 输出预测结果，形状为(batch_size, 10)
        """
        # 第一个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv1(x)))
        # 第二个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv2(x)))
        # 第三个卷积层 + ReLU激活 + 池化
        x = self.pool(F.relu(self.conv3(x)))
        # 展平特征图为一维向量
        x = x.view(-1, 128 * 4 * 4)
        # 应用Dropout
        x = self.dropout(x)
        # 第一个全连接层 + ReLU激活
        x = F.relu(self.fc1(x))
        # 再次应用Dropout
        x = self.dropout(x)
        # 第二个全连接层（输出层）
        x = self.fc2(x)
        return x



class ResidualBlock(nn.Module):
    """
    残差块（Residual Block）
    ResNet网络的基本构建单元，包含跳跃连接
    """
    def __init__(self, inchannel, outchannel, stride=1):
        """
        初始化残差块
        Args:
            inchannel: 输入通道数
            outchannel: 输出通道数
            stride: 卷积步长，默认为1
        """
        super(ResidualBlock, self).__init__()
        # 主路径：两个3x3卷积层 + 批归一化 + ReLU激活
        self.left = nn.Sequential(
            # 第一个卷积层
            nn.Conv2d(inchannel, outchannel, kernel_size=3, stride=stride, padding=1, bias=False),
            nn.BatchNorm2d(outchannel),  # 批归一化
            nn.ReLU(inplace=True),       # ReLU激活函数
            # 第二个卷积层
            nn.Conv2d(outchannel, outchannel, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(outchannel)   # 批归一化
        )
        # 跳跃连接路径（shortcut）
        self.shortcut = nn.Sequential()
        # 如果输入输出维度不匹配，需要调整跳跃连接
        if stride != 1 or inchannel != outchannel:
            self.shortcut = nn.Sequential(
                # 使用1x1卷积调整维度
                nn.Conv2d(inchannel, outchannel, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(outchannel)
            )

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入特征图
        Returns:
            out: 输出特征图（主路径 + 跳跃连接）
        """
        # 主路径的输出
        out = self.left(x)
        # 加上跳跃连接的输出
        out += self.shortcut(x)
        # 最终的ReLU激活
        out = F.relu(out)
        return out


class ResNet(nn.Module):
    """
    ResNet（残差网络）模型
    使用残差块构建的深度神经网络，有效解决梯度消失问题
    """
    def __init__(self, ResidualBlock, num_classes=10):
        """
        初始化ResNet网络
        Args:
            ResidualBlock: 残差块类
            num_classes: 分类类别数，默认为10
        """
        super(ResNet, self).__init__()
        # 当前输入通道数，用于构建残差块
        self.inchannel = 64
        # 初始卷积层：输入3通道，输出64通道
        self.conv1 = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(64),  # 批归一化
            nn.ReLU(),           # ReLU激活函数
        )
        # 四个残差层，每层包含多个残差块
        self.layer1 = self.make_layer(ResidualBlock, 64,  2, stride=1)  # 64通道，2个残差块
        self.layer2 = self.make_layer(ResidualBlock, 128, 2, stride=2)  # 128通道，2个残差块
        self.layer3 = self.make_layer(ResidualBlock, 256, 2, stride=2)  # 256通道，2个残差块
        self.layer4 = self.make_layer(ResidualBlock, 512, 2, stride=2)  # 512通道，2个残差块
        # 最终的全连接层（分类器）
        self.fc = nn.Linear(512, num_classes)

    def make_layer(self, block, channels, num_blocks, stride):
        """
        构建残差层
        Args:
            block: 残差块类
            channels: 输出通道数
            num_blocks: 残差块数量
            stride: 第一个残差块的步长
        Returns:
            nn.Sequential: 包含多个残差块的序列模块
        """
        # 步长列表：第一个块使用指定步长，其余块步长为1
        strides = [stride] + [1] * (num_blocks - 1)   # 例如：strides=[2,1]
        layers = []
        # 逐个添加残差块
        for stride in strides:
            layers.append(block(self.inchannel, channels, stride))
            # 更新输入通道数为当前层的输出通道数
            self.inchannel = channels
        return nn.Sequential(*layers)

    def forward(self, x):
        """
        前向传播函数
        Args:
            x: 输入数据，形状为(batch_size, 3, 32, 32)
        Returns:
            out: 输出预测结果，形状为(batch_size, num_classes)
        """
        # 初始卷积层
        out = self.conv1(x)
        # 四个残差层
        out = self.layer1(out)
        out = self.layer2(out)
        out = self.layer3(out)
        out = self.layer4(out)
        # 全局平均池化，池化核大小为4
        out = F.avg_pool2d(out, 4)
        # 展平为一维向量
        out = out.view(out.size(0), -1)
        # 全连接层进行分类
        out = self.fc(out)
        return out


def ResNet18():
    """
    构建ResNet-18模型
    Returns:
        ResNet: ResNet-18网络实例
    """
    return ResNet(ResidualBlock)
