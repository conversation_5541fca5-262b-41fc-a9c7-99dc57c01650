# 导入必要的库
import numpy
import numpy as np
import matplotlib.pyplot as plt
import os
import torch

if __name__ == '__main__':
    """
    测试和可视化脚本
    用于测试功能和绘制实验结果图表
    """

    # 测试保存数组功能
    accuracy = [1,2]
    r = os.getcwd()
    np.savetxt(r + '\\accu', accuracy)

    # 测试PyTorch的one-hot编码功能
    label = np.random.randint(0, 10, size=(8, 1))  # 生成随机标签
    label = torch.LongTensor(label)                 # 转换为PyTorch张量
    # label = [2,3,4]
    print(torch.nn.functional.one_hot(label, num_classes=10))  # 转换为one-hot编码
    # ones = torch.sparse.torch.eye(10)
    # print(ones.index_select(0,label,None))

    # 加载并绘制CIFAR-10实验结果
    nIID42 = np.loadtxt(
        os.getcwd() + "\Cifar10-Accuracy-cnn2,nonIID.txt")
    plt.plot(nIID42, marker='o', markersize=5)  # 绘制准确率曲线
    plt.xlabel("Epoch")                         # X轴标签
    plt.ylabel("Accuracy")                      # Y轴标签
    plt.grid()                                  # 显示网格
    plt.show()                                  # 显示图表
    # 以下是各种实验结果的可视化代码（已注释）

    # 测试列表操作的代码示例（已注释）
    # c = [1,1,1,1,0,0,0,0,0]
    # b = [1,1]
    # print(list(set(c)-set(b)))  # 集合差运算
    # print(c.count(0))           # 统计0的个数
    # print([i for i, x in enumerate(c) if x == 0])  # 找到值为0的索引
    # index = [numpy.random.permutation(4)[0:3]]
    # print(index)
    # print([numpy.random.permutation(4)[0:3]])
    # for i in range(8):
    #     print(numpy.random.permutation(4))
    # r = os.getcwd() + "\IID\FedProx_AsyAccuracy"
    # print("./IID/FedProx_AsyAccuracy")

    # Non-IID环境下不同延迟比例的FedProx实验结果对比（已注释）
    # nIID42 = np.loadtxt(
    #     os.getcwd() + "\\nonIID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.2,mu=0.01,newWeight).txt")
    # nIID44 = np.loadtxt(
    #     os.getcwd() + "\\nonIID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.4,mu=0.001,newWeight).txt")
    # nIID46 = np.loadtxt(
    #     os.getcwd() + "\\nonIID\AsyAccuracy\AsyAccuracy(staleThreshold=4,percent=0.6,newWeight1).txt")
    # nIID48 = np.loadtxt(
    #     os.getcwd() + "\\nonIID\AsyAccuracy\AsyAccuracy(staleThreshold=4,percent=0.8,newWeight1).txt")
    # proxnIID = np.loadtxt(os.getcwd() + "\\nonIID\AccuracyProx,nonIID,mu=0.001.txt")
    # avgnIID = np.loadtxt(os.getcwd() + "\\nonIID\Accuracy,nonIID.txt")
    #
    # plt.plot(nIID42, marker='o', markersize=5)  # 绘制折线图，添加数据点，设置点的大小
    # plt.plot(nIID44, marker='o', markersize=5)
    # plt.plot(nIID46, marker='o', markersize=5)
    # plt.plot(nIID48, marker='o', markersize=5)
    # plt.plot(proxnIID, marker='o', markersize=5)
    # plt.plot(avgnIID, marker='o', markersize=5)
    # plt.legend(['20%', '40%', '60%',
    #             '80%', 'FedProx', 'FedAvg', ])  # 图例：不同延迟比例和算法
    #
    # plt.title('non-IID,staleThreshold=4')  # 图标题
    # plt.xlabel("Epoch")
    # plt.ylabel("Accuracy")
    # plt.grid()
    # plt.show()

    # IID环境下不同延迟比例的FedProx实验结果对比（已注释）
    # IID42 = np.loadtxt(os.getcwd() + "\IID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.2,mu=0.1,newWeight).txt")
    # IID44 = np.loadtxt(os.getcwd() + "\IID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.4,mu=0.1,newWeight).txt")
    # IID46 = np.loadtxt(os.getcwd() + "\IID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.6,mu=0.1,newWeight).txt")
    # IID48 = np.loadtxt(os.getcwd() + "\IID\FedProx_AsyAccuracy\FedProx_AsyAccuracy(staleThreshold=4,percent=0.8,mu=0.1,newWeight).txt")
    # proxIID = np.loadtxt(os.getcwd() + "\IID\AccuracyProx,IID,mu=0.01.txt")
    # avgIID = np.loadtxt(os.getcwd() + "\IID\Accuracy,IID.txt")
    #
    # plt.plot(IID42, marker='o', markersize=5)  # 绘制折线图，添加数据点，设置点的大小
    # plt.plot(IID44, marker='o', markersize=5)
    # plt.plot(IID46, marker='o', markersize=5)
    # plt.plot(IID48, marker='o', markersize=5)
    # plt.plot(proxIID, marker='o', markersize=5)
    # plt.plot(avgIID, marker='o', markersize=5)
    # plt.legend(['20%', '40%', '60%',
    #             '80%','FedProx','FedAvg',])  # 图例：不同延迟比例和算法
    #
    # plt.title('IID,staleThreshold=4')  # 图标题
    # plt.xlabel("Epoch")
    # plt.ylabel("Accuracy")
    # plt.grid()
    # plt.show()

    # FedAvg和FedProx在IID和Non-IID环境下的性能对比
    fedavgIID = np.loadtxt(os.getcwd() + "\IID\Accuracy,IID.txt")                    # FedAvg IID结果
    fedproxIID = np.loadtxt(os.getcwd() + "\IID\AccuracyProx,IID,mu=0.01.txt")       # FedProx IID结果
    fedavgNIID = np.loadtxt(os.getcwd() + "\\nonIID\Accuracy,nonIID.txt")           # FedAvg Non-IID结果
    fedproxNIID = np.loadtxt(os.getcwd() + "\\nonIID\AccuracyProx,nonIID,mu=0.001.txt")  # FedProx Non-IID结果

    # 绘制对比图
    plt.plot(fedavgIID, marker='o', markersize=5)      # FedAvg IID
    plt.plot(fedproxIID, marker='o', markersize=5)     # FedProx IID
    plt.plot(fedavgNIID[0:50], marker='o', markersize=5)   # FedAvg Non-IID（前50轮）
    plt.plot(fedproxNIID[0:50], marker='o', markersize=5)  # FedProx Non-IID（前50轮）

    # 设置图例和标签
    plt.legend(['FedAvg,iid',  'FedProx,iid', 'FedAvg,non-iid', 'FedProx,non-iid'])
    plt.xlabel("Epoch")
    plt.ylabel("Accuracy")
    plt.grid()
    plt.show()


    # 异步联邦学习算法的性能对比（已注释）
    # 包含不同延迟阈值和延迟比例的实验结果
    # FedAvg = np.loadtxt("Accuracy,nonIID.txt")  # FedAvg基准结果
    # #Asy4_2 = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.2).txt")  # 旧版本结果
    # Asy4_2n = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.2,newWeight).txt")    # 延迟阈值4，延迟比例20%
    # #Asy4_4 = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.4).txt")  # 旧版本结果
    # Asy4_4n = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.4,newWeight).txt")    # 延迟阈值4，延迟比例40%
    # #Asy8_2 = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.2).txt")  # 旧版本结果
    # Asy8_2n = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.2,newWeight1).txt")   # 延迟阈值8，延迟比例20%
    # # Asy8_4 = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.4).txt")  # 旧版本结果
    # # syn8_4 = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.4,withSameWeight).txt")  # 同步版本
    # Asy8_4n = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.4,newWeight).txt")    # 延迟阈值8，延迟比例40%
    # Asy4_6n = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.6,newWeight1).txt")   # 延迟阈值4，延迟比例60%
    # Asy8_6n = np.loadtxt("AsyAccuracy(staleThreshold=8,percent=0.6,newWeight1).txt")   # 延迟阈值8，延迟比例60%
    # Asy4_8n = np.loadtxt("AsyAccuracy(staleThreshold=4,percent=0.8,newWeight1).txt")   # 延迟阈值4，延迟比例80%
    #
    # # 绘制所有算法的性能对比图
    # plt.plot(FedAvg, marker='o', markersize=3)    # FedAvg基准
    # plt.plot(Asy4_2n, marker='o', markersize=3)   # AsyFed变体1
    # #plt.plot(Asy4_4, marker='o', markersize=3)   # 旧版本（已注释）
    # plt.plot(Asy4_4n, marker='o', markersize=3)   # AsyFed变体2
    # plt.plot(Asy8_2n, marker='o', markersize=3)   # AsyFed变体3
    # # plt.plot(Asy8_4, marker='o', markersize=3)   # 旧版本（已注释）
    # # plt.plot(syn8_4, marker='o', markersize=3)   # 同步版本（已注释）
    # plt.plot(Asy8_4n, marker='o', markersize=3)   # AsyFed变体4
    # plt.plot(Asy4_6n, marker='o', markersize=3)   # AsyFed变体5
    # plt.plot(Asy8_6n, marker='o', markersize=3)   # AsyFed变体6
    # plt.plot(Asy4_8n, marker='o', markersize=3)   # AsyFed变体7
    #
    # # 设置图例，说明各条曲线代表的算法配置
    # plt.legend(['FedAvg',  'AsyFed_4_0.2n', 'synFed_4_0.4n', 'AsyFed_8_0.2n', 'AsyFed_8_0.4n', 'AsyFed_4_0.6n', 'AsyFed_8_0.6n', 'AsyFed_4_0.8n'])
    # # 图例说明：AsyFed_延迟阈值_延迟比例
    #
    # plt.show()  # 显示图表
