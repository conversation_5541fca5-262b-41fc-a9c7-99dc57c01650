#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联邦学习算法综合对比试验
包含以下算法的对比：
1. CAFL (Clustered Asynchronous Federated Learning) - 基于密度峰值聚类的异步联邦学习
2. APPAFL (Asynchronous Personalized and Privacy-preserving Federated Learning) - 异步个性化隐私保护联邦学习
3. SWIM-APPAFL - SWIM对比学习与APPAFL的融合算法
4. FedAvg - 标准联邦平均算法（基准）

作者：基于2021ZhangAFLDCS-master、APPAFL-main、SWIM-main的综合实现
"""

import os
import sys
import argparse
import time
import copy
import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json
from datetime import datetime

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入各算法的实现模块
try:
    # SWIM-APPAFL集成模块
    sys.path.append('./SWIM-APPAFL-Integration')
    from SWIM_APPAFL_Integration.main import create_model as create_swim_appafl_model
    from SWIM_APPAFL_Integration.clients import ClientsGroup as SWIMAPPAFLClientsGroup
    from SWIM_APPAFL_Integration.server import APPAFLServer
    from SWIM_APPAFL_Integration.datasets import get_dataset as get_swim_appafl_dataset
    
    # APPAFL模块
    sys.path.append('./APPAFL-main')
    from APPAFL_main.Models import CNNCifar as APPAFLCNNCifar
    from APPAFL_main.clients import ClientsGroup as APPAFLClientsGroup
    from APPAFL_main.server import Server as APPAFLServer_Original
    
    # SWIM模块
    sys.path.append('./SWIM-main')
    from SWIM_main.main import init_nets as init_swim_nets
    from SWIM_main.main import local_train_net as swim_local_train
    
except ImportError as e:
    print(f"警告：部分模块导入失败: {e}")
    print("将使用简化的实现版本")


class ExperimentConfig:
    """实验配置类"""
    def __init__(self):
        # 基础实验参数
        self.dataset = 'cifar10'  # 数据集选择: mnist, cifar10, cifar100, fashion_mnist
        self.model = 'cnn'        # 模型选择: cnn, resnet18, resnet50
        self.num_clients = 100    # 客户端总数
        self.num_rounds = 100     # 通信轮数
        self.local_epochs = 5     # 本地训练轮数
        self.batch_size = 32      # 批次大小
        self.learning_rate = 0.01 # 学习率
        
        # 数据分布参数
        self.iid = False          # 是否使用IID数据分布
        self.alpha = 0.2          # Dirichlet分布参数（控制非IID程度）
        
        # 异步联邦学习参数
        self.client_fraction = 0.1    # 每轮参与的客户端比例
        self.stale_threshold = 4      # 延迟阈值
        self.aggregation_threshold = 6 # 聚合阈值
        
        # SWIM算法参数
        self.temperature = 0.5    # 对比学习温度参数
        self.mu = 0.5            # SWIM动态权重参数
        self.kr = 0.5            # 滑动窗口比例参数
        self.out_dim = 256       # 投影维度
        
        # CAFL算法参数
        self.dc = -0.02          # 密度峰值聚类参数
        self.cluster_threshold = 0.5  # 聚类阈值
        
        # 实验控制参数
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.seed = 42           # 随机种子
        self.save_results = True # 是否保存结果
        self.plot_results = True # 是否绘制结果图
        
        # 算法选择（可以选择运行哪些算法）
        self.algorithms = ['fedavg', 'appafl', 'swim_appafl', 'cafl']


class FederatedLearningComparison:
    """联邦学习算法对比实验主类"""
    
    def __init__(self, config):
        self.config = config
        self.results = defaultdict(dict)  # 存储各算法的实验结果
        self.setup_environment()
        
    def setup_environment(self):
        """设置实验环境"""
        # 设置随机种子
        random.seed(self.config.seed)
        np.random.seed(self.config.seed)
        torch.manual_seed(self.config.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.config.seed)
            torch.cuda.manual_seed_all(self.config.seed)
        
        # 设置设备
        self.device = torch.device(self.config.device)
        print(f"使用设备: {self.device}")
        
        # 创建结果保存目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f"comparison_results_{self.timestamp}"
        if self.config.save_results:
            os.makedirs(self.results_dir, exist_ok=True)
    
    def load_data(self):
        """加载和预处理数据"""
        print(f"加载数据集: {self.config.dataset}")
        
        # 这里需要实现统一的数据加载接口
        # 暂时使用简化版本
        if self.config.dataset == 'cifar10':
            from torchvision import datasets, transforms
            transform = transforms.Compose([
                transforms.ToTensor(),
                transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
            ])
            
            train_dataset = datasets.CIFAR10(
                root='./data', train=True, download=True, transform=transform
            )
            test_dataset = datasets.CIFAR10(
                root='./data', train=False, download=True, transform=transform
            )
            
            self.num_classes = 10
            self.input_channels = 3
            self.input_size = 32
            
        else:
            raise NotImplementedError(f"数据集 {self.config.dataset} 尚未实现")
        
        self.train_dataset = train_dataset
        self.test_dataset = test_dataset
        
        # 创建测试数据加载器
        self.test_loader = DataLoader(
            test_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=False
        )
        
        print(f"训练集大小: {len(train_dataset)}, 测试集大小: {len(test_dataset)}")
    
    def partition_data(self):
        """数据分区 - 为不同客户端分配数据"""
        print("进行数据分区...")
        
        if self.config.iid:
            # IID数据分区
            indices = list(range(len(self.train_dataset)))
            random.shuffle(indices)
            
            client_data_indices = []
            samples_per_client = len(indices) // self.config.num_clients
            
            for i in range(self.config.num_clients):
                start_idx = i * samples_per_client
                end_idx = start_idx + samples_per_client
                client_data_indices.append(indices[start_idx:end_idx])
        else:
            # 非IID数据分区（使用Dirichlet分布）
            client_data_indices = self.dirichlet_partition()
        
        self.client_data_indices = client_data_indices
        print(f"数据分区完成，每个客户端平均样本数: {np.mean([len(indices) for indices in client_data_indices]):.1f}")
    
    def dirichlet_partition(self):
        """使用Dirichlet分布进行非IID数据分区"""
        # 获取标签
        targets = np.array([self.train_dataset[i][1] for i in range(len(self.train_dataset))])
        
        # 为每个类别创建索引
        class_indices = [np.where(targets == i)[0] for i in range(self.num_classes)]
        
        client_data_indices = [[] for _ in range(self.config.num_clients)]
        
        for class_idx in range(self.num_classes):
            # 使用Dirichlet分布分配每个类别的样本
            proportions = np.random.dirichlet(np.repeat(self.config.alpha, self.config.num_clients))
            proportions = np.array([p * (len(idx) < len(self.train_dataset) / self.config.num_clients) 
                                  for p, idx in zip(proportions, client_data_indices)])
            proportions = proportions / proportions.sum()
            proportions = (np.cumsum(proportions) * len(class_indices[class_idx])).astype(int)[:-1]
            
            # 分配样本索引
            split_indices = np.split(class_indices[class_idx], proportions)
            for client_idx, indices in enumerate(split_indices):
                client_data_indices[client_idx].extend(indices)
        
        # 打乱每个客户端的数据
        for client_idx in range(self.config.num_clients):
            random.shuffle(client_data_indices[client_idx])
        
        return client_data_indices
    
    def create_model(self, algorithm='fedavg'):
        """根据算法类型创建模型"""
        if algorithm in ['fedavg', 'appafl']:
            # 标准CNN模型
            if self.config.model == 'cnn':
                from models import CNNCifar  # 需要实现统一的模型接口
                model = CNNCifar(num_classes=self.num_classes)
            else:
                raise NotImplementedError(f"模型 {self.config.model} 尚未实现")
                
        elif algorithm == 'swim_appafl':
            # SWIM模型（带投影头）
            model = create_swim_appafl_model(self.config)
            
        elif algorithm == 'cafl':
            # CAFL模型（需要适配）
            model = self.create_cafl_model()
            
        else:
            raise ValueError(f"未知算法: {algorithm}")
        
        return model.to(self.device)
    
    def create_cafl_model(self):
        """创建CAFL算法的模型（从TensorFlow适配到PyTorch）"""
        # 这里需要将2021ZhangAFLDCS-master中的TensorFlow模型适配到PyTorch
        # 暂时使用简化版本
        class CAFLModel(nn.Module):
            def __init__(self, num_classes):
                super(CAFLModel, self).__init__()
                self.conv1 = nn.Conv2d(3, 32, 3, padding=1)
                self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
                self.pool = nn.MaxPool2d(2, 2)
                self.fc1 = nn.Linear(64 * 8 * 8, 512)
                self.fc2 = nn.Linear(512, num_classes)
                self.dropout = nn.Dropout(0.5)
            
            def forward(self, x):
                x = self.pool(F.relu(self.conv1(x)))
                x = self.pool(F.relu(self.conv2(x)))
                x = x.view(-1, 64 * 8 * 8)
                x = F.relu(self.fc1(x))
                x = self.dropout(x)
                x = self.fc2(x)
                return x
        
        return CAFLModel(self.num_classes)
    
    def run_experiment(self):
        """运行完整的对比实验"""
        print("=" * 60)
        print("开始联邦学习算法对比实验")
        print("=" * 60)
        
        # 数据准备
        self.load_data()
        self.partition_data()
        
        # 运行各个算法
        for algorithm in self.config.algorithms:
            print(f"\n{'='*20} 运行 {algorithm.upper()} 算法 {'='*20}")
            start_time = time.time()
            
            try:
                if algorithm == 'fedavg':
                    results = self.run_fedavg()
                elif algorithm == 'appafl':
                    results = self.run_appafl()
                elif algorithm == 'swim_appafl':
                    results = self.run_swim_appafl()
                elif algorithm == 'cafl':
                    results = self.run_cafl()
                else:
                    print(f"未知算法: {algorithm}")
                    continue
                
                end_time = time.time()
                results['training_time'] = end_time - start_time
                self.results[algorithm] = results
                
                print(f"{algorithm.upper()} 算法完成，用时: {end_time - start_time:.2f}秒")
                print(f"最终测试准确率: {results['test_accuracies'][-1]:.4f}")
                
            except Exception as e:
                print(f"运行 {algorithm} 时出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 分析和可视化结果
        self.analyze_results()
        if self.config.plot_results:
            self.plot_comparison()
        
        # 保存结果
        if self.config.save_results:
            self.save_experiment_results()
    
    def run_fedavg(self):
        """运行FedAvg算法"""
        print("初始化FedAvg算法...")

        # 创建模型
        global_model = self.create_model('fedavg')

        # 初始化结果记录
        results = {
            'test_accuracies': [],
            'train_losses': [],
            'communication_rounds': [],
            'convergence_round': None
        }

        # FedAvg主循环
        for round_num in range(self.config.num_rounds):
            print(f"FedAvg - 第 {round_num + 1}/{self.config.num_rounds} 轮")

            # 选择客户端
            num_selected = max(1, int(self.config.client_fraction * self.config.num_clients))
            selected_clients = random.sample(range(self.config.num_clients), num_selected)

            # 客户端本地训练
            client_models = []
            total_loss = 0
            for client_id in selected_clients:
                client_model = copy.deepcopy(global_model)
                loss = self.local_train_fedavg(client_model, client_id)
                client_models.append(client_model.state_dict())
                total_loss += loss

            # 服务器聚合
            global_params = self.fedavg_aggregate(client_models)
            global_model.load_state_dict(global_params)

            # 评估
            test_acc = self.evaluate_model(global_model)
            avg_loss = total_loss / len(selected_clients)

            results['test_accuracies'].append(test_acc)
            results['train_losses'].append(avg_loss)
            results['communication_rounds'].append(round_num + 1)

            print(f"  测试准确率: {test_acc:.4f}, 平均训练损失: {avg_loss:.4f}")

        return results

    def local_train_fedavg(self, model, client_id):
        """FedAvg客户端本地训练"""
        model.train()
        optimizer = torch.optim.SGD(model.parameters(), lr=self.config.learning_rate, momentum=0.9)
        criterion = nn.CrossEntropyLoss()

        # 获取客户端数据
        client_indices = self.client_data_indices[client_id]
        client_dataset = torch.utils.data.Subset(self.train_dataset, client_indices)
        client_loader = DataLoader(client_dataset, batch_size=self.config.batch_size, shuffle=True)

        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.local_epochs):
            for batch_idx, (data, target) in enumerate(client_loader):
                data, target = data.to(self.device), target.to(self.device)

                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches if num_batches > 0 else 0

    def run_appafl(self):
        """运行APPAFL算法"""
        print("初始化APPAFL算法...")

        # 创建模型和服务器
        global_model = self.create_model('appafl')
        server = APPAFLServer(
            model=global_model,
            device=self.device,
            n_clients=self.config.num_clients,
            cfraction=self.config.client_fraction
        )

        # 初始化结果记录
        results = {
            'test_accuracies': [],
            'train_losses': [],
            'communication_rounds': [],
            'convergence_round': None
        }

        # APPAFL主循环
        for round_num in range(self.config.num_rounds):
            print(f"APPAFL - 第 {round_num + 1}/{self.config.num_rounds} 轮")

            # 选择客户端（包括延迟客户端处理）
            training_clients, stale_model_clients = server.select_clients(round_num)
            all_clients = training_clients + stale_model_clients

            if not all_clients:
                print("警告：没有客户端参与本轮训练")
                continue

            # 客户端训练
            client_models = []
            client_ids = []
            total_loss = 0

            for client_id in all_clients:
                # 获取陈旧模型参数
                stale_params = server.get_stale_model(client_id, round_num)

                # 本地训练
                client_model = copy.deepcopy(global_model)
                loss = self.local_train_appafl(client_model, client_id, stale_params)

                client_models.append(client_model.state_dict())
                client_ids.append(client_id)
                total_loss += loss

            # 服务器聚合
            aggregated_params = server.aggregate_models(client_models, client_ids, round_num)
            global_model.load_state_dict(aggregated_params)
            server.global_parameters = aggregated_params

            # 更新服务器状态
            server.update_client_flags(round_num, all_clients)

            # 评估
            test_acc = self.evaluate_model(global_model)
            avg_loss = total_loss / len(all_clients) if all_clients else 0

            results['test_accuracies'].append(test_acc)
            results['train_losses'].append(avg_loss)
            results['communication_rounds'].append(round_num + 1)

            print(f"  测试准确率: {test_acc:.4f}, 平均训练损失: {avg_loss:.4f}")

        return results

    def local_train_appafl(self, model, client_id, global_params):
        """APPAFL客户端本地训练"""
        model.load_state_dict(global_params)
        model.train()

        optimizer = torch.optim.SGD(model.parameters(), lr=self.config.learning_rate, momentum=0.9)
        criterion = nn.CrossEntropyLoss()

        # 获取客户端数据
        client_indices = self.client_data_indices[client_id]
        client_dataset = torch.utils.data.Subset(self.train_dataset, client_indices)
        client_loader = DataLoader(client_dataset, batch_size=self.config.batch_size, shuffle=True)

        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.local_epochs):
            for batch_idx, (data, target) in enumerate(client_loader):
                data, target = data.to(self.device), target.to(self.device)

                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches if num_batches > 0 else 0

    def run_swim_appafl(self):
        """运行SWIM-APPAFL融合算法"""
        print("初始化SWIM-APPAFL算法...")

        # 创建模型和服务器
        global_model = self.create_model('swim_appafl')
        server = APPAFLServer(
            model=global_model,
            device=self.device,
            n_clients=self.config.num_clients,
            cfraction=self.config.client_fraction
        )

        # SWIM算法相关变量
        global_model_for_swim = copy.deepcopy(global_model)
        global_model_for_swim.to('cpu')  # 保持在CPU上节省显存
        historical_models = []

        # 初始化结果记录
        results = {
            'test_accuracies': [],
            'train_losses': [],
            'communication_rounds': [],
            'convergence_round': None
        }

        # SWIM-APPAFL主循环
        for round_num in range(self.config.num_rounds):
            print(f"SWIM-APPAFL - 第 {round_num + 1}/{self.config.num_rounds} 轮")

            # 选择客户端
            training_clients, stale_model_clients = server.select_clients(round_num)
            all_clients = training_clients + stale_model_clients

            if not all_clients:
                print("警告：没有客户端参与本轮训练")
                continue

            # 客户端训练
            client_models = []
            client_ids = []
            total_loss = 0

            for client_id in all_clients:
                # 获取陈旧模型参数
                stale_params = server.get_stale_model(client_id, round_num)

                # 使用SWIM算法进行本地训练
                client_model = copy.deepcopy(global_model)
                loss = self.local_train_swim_appafl(
                    client_model, client_id, stale_params,
                    global_model_for_swim, historical_models, round_num
                )

                client_models.append(client_model.state_dict())
                client_ids.append(client_id)
                total_loss += loss

            # 服务器聚合
            aggregated_params = server.aggregate_models(client_models, client_ids, round_num)
            global_model.load_state_dict(aggregated_params)
            server.global_parameters = aggregated_params

            # 更新SWIM相关组件
            global_model_for_swim.load_state_dict(aggregated_params)
            global_model_for_swim.to('cpu')

            # 更新历史模型（保持滑动窗口）
            window_size = max(1, int(self.config.kr * self.config.num_rounds))
            if len(historical_models) >= window_size:
                historical_models.pop(0)
            historical_models.append(copy.deepcopy(global_model_for_swim))

            # 更新服务器状态
            server.update_client_flags(round_num, all_clients)

            # 评估
            test_acc = self.evaluate_model(global_model)
            avg_loss = total_loss / len(all_clients) if all_clients else 0

            results['test_accuracies'].append(test_acc)
            results['train_losses'].append(avg_loss)
            results['communication_rounds'].append(round_num + 1)

            print(f"  测试准确率: {test_acc:.4f}, 平均训练损失: {avg_loss:.4f}")

        return results

    def local_train_swim_appafl(self, model, client_id, global_params,
                               global_model, historical_models, round_num):
        """SWIM-APPAFL客户端本地训练（包含对比学习）"""
        model.load_state_dict(global_params)
        model.train()

        optimizer = torch.optim.SGD(model.parameters(), lr=self.config.learning_rate, momentum=0.9)
        criterion = nn.CrossEntropyLoss()
        cos = nn.CosineSimilarity(dim=-1)

        # 计算动态权重μ（基于本地训练轮次）
        mu = 0.5 - (round_num + 1) / (2 * self.config.num_rounds)
        mu = max(0.1, mu)  # 确保权重不会太小

        # 获取客户端数据
        client_indices = self.client_data_indices[client_id]
        client_dataset = torch.utils.data.Subset(self.train_dataset, client_indices)
        client_loader = DataLoader(client_dataset, batch_size=self.config.batch_size, shuffle=True)

        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.local_epochs):
            for batch_idx, (data, target) in enumerate(client_loader):
                data, target = data.to(self.device), target.to(self.device)

                optimizer.zero_grad()

                # 前向传播
                features, projection, classification = model(data)

                # 分类损失
                classification_loss = criterion(classification, target)

                # 对比学习损失（SWIM算法核心）
                contrastive_loss = 0.0
                if global_model is not None and len(historical_models) > 0:
                    # 计算对比学习损失
                    contrastive_loss = self.compute_swim_contrastive_loss(
                        projection, global_model, historical_models, data
                    )

                # 总损失：分类损失 + 动态权重 * 对比学习损失
                total_batch_loss = classification_loss + mu * contrastive_loss

                total_batch_loss.backward()
                optimizer.step()

                total_loss += total_batch_loss.item()
                num_batches += 1

        return total_loss / num_batches if num_batches > 0 else 0

    def compute_swim_contrastive_loss(self, projection, global_model, historical_models, data):
        """计算SWIM对比学习损失"""
        try:
            global_model.to(self.device)
            global_model.eval()

            with torch.no_grad():
                # 获取全局模型的投影特征
                _, global_projection, _ = global_model(data)

            # 计算正样本相似度（当前模型与全局模型）
            pooled_current = torch.mean(projection, dim=0, keepdim=True)
            pooled_global = torch.mean(global_projection, dim=0, keepdim=True)

            cos = nn.CosineSimilarity(dim=-1)
            posi = torch.exp(torch.mean(cos(pooled_current, pooled_global).reshape(-1, 1)) / self.config.temperature)

            # 计算负样本相似度（与历史模型）
            nega = torch.exp(torch.mean(cos(pooled_current, pooled_current).reshape(-1, 1)) / self.config.temperature) - torch.exp(
                torch.mean(cos(pooled_current, pooled_current).reshape(-1, 1)) / self.config.temperature)

            for hist_model in historical_models[-3:]:  # 使用最近的3个历史模型
                hist_model.to(self.device)
                hist_model.eval()
                with torch.no_grad():
                    _, hist_projection, _ = hist_model(data)

                pooled_hist = torch.mean(hist_projection, dim=0, keepdim=True)
                nega += torch.exp(torch.mean(cos(pooled_current, pooled_hist).reshape(-1, 1)) / self.config.temperature)
                hist_model.to('cpu')

            # SWIM对比学习损失
            contrastive_loss = torch.mean(-torch.log(posi / (posi + nega)))

            global_model.to('cpu')
            return contrastive_loss

        except Exception as e:
            print(f"计算对比学习损失时出错: {e}")
            return torch.tensor(0.0, device=self.device)

    def fedavg_aggregate(self, client_models):
        """FedAvg聚合算法"""
        if not client_models:
            return {}

        # 简单平均聚合
        aggregated_params = {}
        for key in client_models[0].keys():
            aggregated_params[key] = torch.stack([
                client_model[key] for client_model in client_models
            ]).mean(dim=0)

        return aggregated_params

    def run_cafl(self):
        """运行CAFL算法（基于密度峰值聚类的异步联邦学习）"""
        print("初始化CAFL算法...")

        # 创建模型
        global_model = self.create_model('cafl')

        # 初始化CAFL特有的组件
        client_clusters = {}  # 客户端聚类结果
        cluster_models = {}   # 每个聚类的模型

        # 初始化结果记录
        results = {
            'test_accuracies': [],
            'train_losses': [],
            'communication_rounds': [],
            'convergence_round': None,
            'clustering_results': []
        }

        # CAFL主循环
        for round_num in range(self.config.num_rounds):
            print(f"CAFL - 第 {round_num + 1}/{self.config.num_rounds} 轮")

            # 选择客户端
            num_selected = max(1, int(self.config.client_fraction * self.config.num_clients))
            selected_clients = random.sample(range(self.config.num_clients), num_selected)

            # 客户端本地训练
            client_models = []
            client_weights = []
            total_loss = 0

            for client_id in selected_clients:
                client_model = copy.deepcopy(global_model)
                loss = self.local_train_cafl(client_model, client_id)
                client_models.append(client_model.state_dict())
                client_weights.append(client_model.state_dict())
                total_loss += loss

            # 执行密度峰值聚类（每5轮执行一次）
            if round_num % 5 == 0 and len(client_models) > 1:
                client_clusters = self.density_peaks_clustering(client_weights, selected_clients)
                results['clustering_results'].append({
                    'round': round_num,
                    'clusters': client_clusters
                })
                print(f"  聚类结果: {len(set(client_clusters.values()))} 个聚类")

            # 基于聚类的聚合
            if client_clusters:
                aggregated_params = self.cafl_cluster_aggregate(client_models, selected_clients, client_clusters)
            else:
                # 如果没有聚类信息，使用标准聚合
                aggregated_params = self.fedavg_aggregate(client_models)

            global_model.load_state_dict(aggregated_params)

            # 评估
            test_acc = self.evaluate_model(global_model)
            avg_loss = total_loss / len(selected_clients)

            results['test_accuracies'].append(test_acc)
            results['train_losses'].append(avg_loss)
            results['communication_rounds'].append(round_num + 1)

            print(f"  测试准确率: {test_acc:.4f}, 平均训练损失: {avg_loss:.4f}")

        return results

    def local_train_cafl(self, model, client_id):
        """CAFL客户端本地训练"""
        model.train()
        optimizer = torch.optim.SGD(model.parameters(), lr=self.config.learning_rate, momentum=0.9)
        criterion = nn.CrossEntropyLoss()

        # 获取客户端数据
        client_indices = self.client_data_indices[client_id]
        client_dataset = torch.utils.data.Subset(self.train_dataset, client_indices)
        client_loader = DataLoader(client_dataset, batch_size=self.config.batch_size, shuffle=True)

        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.local_epochs):
            for batch_idx, (data, target) in enumerate(client_loader):
                data, target = data.to(self.device), target.to(self.device)

                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches if num_batches > 0 else 0

    def density_peaks_clustering(self, client_weights, client_ids):
        """密度峰值聚类算法（简化版本）"""
        try:
            # 计算客户端模型之间的距离矩阵
            n_clients = len(client_weights)
            distance_matrix = np.zeros((n_clients, n_clients))

            for i in range(n_clients):
                for j in range(i + 1, n_clients):
                    # 计算模型参数的L2距离
                    dist = 0
                    for key in client_weights[i].keys():
                        if 'weight' in key:  # 只考虑权重参数
                            w1 = client_weights[i][key].cpu().numpy().flatten()
                            w2 = client_weights[j][key].cpu().numpy().flatten()
                            dist += np.linalg.norm(w1 - w2) ** 2

                    distance_matrix[i][j] = distance_matrix[j][i] = np.sqrt(dist)

            # 简化的密度峰值聚类
            # 计算局部密度
            dc = np.percentile(distance_matrix[distance_matrix > 0], 2)  # 使用2%分位数作为截断距离
            rho = np.zeros(n_clients)

            for i in range(n_clients):
                rho[i] = np.sum(distance_matrix[i] < dc) - 1  # 减去自身

            # 计算相对距离
            delta = np.zeros(n_clients)
            for i in range(n_clients):
                if rho[i] == np.max(rho):
                    delta[i] = np.max(distance_matrix[i])
                else:
                    higher_density_indices = np.where(rho > rho[i])[0]
                    delta[i] = np.min(distance_matrix[i][higher_density_indices])

            # 选择聚类中心（简化：选择rho*delta最大的点作为聚类中心）
            gamma = rho * delta
            n_clusters = min(3, max(1, n_clients // 3))  # 动态确定聚类数量
            cluster_centers = np.argsort(gamma)[-n_clusters:]

            # 分配聚类标签
            client_clusters = {}
            for i, client_id in enumerate(client_ids):
                # 找到最近的聚类中心
                distances_to_centers = [distance_matrix[i][center] for center in cluster_centers]
                cluster_label = np.argmin(distances_to_centers)
                client_clusters[client_id] = cluster_label

            return client_clusters

        except Exception as e:
            print(f"聚类过程中出错: {e}")
            # 返回默认聚类（所有客户端在同一聚类）
            return {client_id: 0 for client_id in client_ids}

    def cafl_cluster_aggregate(self, client_models, client_ids, client_clusters):
        """基于聚类的模型聚合"""
        # 按聚类分组
        cluster_groups = defaultdict(list)
        for i, client_id in enumerate(client_ids):
            cluster_label = client_clusters.get(client_id, 0)
            cluster_groups[cluster_label].append(client_models[i])

        # 对每个聚类进行聚合
        cluster_aggregated = {}
        for cluster_label, models in cluster_groups.items():
            cluster_aggregated[cluster_label] = self.fedavg_aggregate(models)

        # 将所有聚类的结果再次聚合
        if len(cluster_aggregated) == 1:
            return list(cluster_aggregated.values())[0]
        else:
            return self.fedavg_aggregate(list(cluster_aggregated.values()))

    def evaluate_model(self, model):
        """评估模型性能"""
        model.eval()
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in self.test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = model(data)

                # 处理不同模型的输出格式
                if isinstance(output, tuple):
                    # SWIM模型返回 (features, projection, classification)
                    _, _, classification = output
                    pred = classification.argmax(dim=1)
                else:
                    pred = output.argmax(dim=1)

                correct += pred.eq(target).sum().item()
                total += target.size(0)

        accuracy = correct / total
        model.train()
        return accuracy

    def analyze_results(self):
        """分析实验结果"""
        print("\n" + "="*60)
        print("实验结果分析")
        print("="*60)

        # 创建结果汇总表
        summary = {}

        for algorithm, results in self.results.items():
            if not results['test_accuracies']:
                continue

            final_accuracy = results['test_accuracies'][-1]
            max_accuracy = max(results['test_accuracies'])

            # 计算收敛轮次（准确率达到最大值90%的轮次）
            convergence_threshold = max_accuracy * 0.9
            convergence_round = None
            for i, acc in enumerate(results['test_accuracies']):
                if acc >= convergence_threshold:
                    convergence_round = i + 1
                    break

            # 计算平均训练损失
            avg_train_loss = np.mean(results['train_losses']) if results['train_losses'] else 0

            summary[algorithm] = {
                'final_accuracy': final_accuracy,
                'max_accuracy': max_accuracy,
                'convergence_round': convergence_round,
                'avg_train_loss': avg_train_loss,
                'training_time': results.get('training_time', 0)
            }

        # 打印结果汇总
        print(f"{'算法':<15} {'最终准确率':<12} {'最高准确率':<12} {'收敛轮次':<10} {'平均损失':<12} {'训练时间(s)':<12}")
        print("-" * 80)

        for algorithm, metrics in summary.items():
            print(f"{algorithm.upper():<15} "
                  f"{metrics['final_accuracy']:<12.4f} "
                  f"{metrics['max_accuracy']:<12.4f} "
                  f"{metrics['convergence_round'] or 'N/A':<10} "
                  f"{metrics['avg_train_loss']:<12.4f} "
                  f"{metrics['training_time']:<12.2f}")

        # 找出最佳算法
        best_algorithm = max(summary.keys(), key=lambda x: summary[x]['final_accuracy'])
        print(f"\n最佳算法: {best_algorithm.upper()} (最终准确率: {summary[best_algorithm]['final_accuracy']:.4f})")

        return summary

    def plot_comparison(self):
        """绘制对比结果图"""
        print("\n绘制结果对比图...")

        # 设置绘图风格
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('联邦学习算法对比实验结果', fontsize=16, fontweight='bold')

        # 颜色映射
        colors = {
            'fedavg': '#1f77b4',
            'appafl': '#ff7f0e',
            'swim_appafl': '#2ca02c',
            'cafl': '#d62728'
        }

        # 算法名称映射
        algorithm_names = {
            'fedavg': 'FedAvg',
            'appafl': 'APPAFL',
            'swim_appafl': 'SWIM-APPAFL',
            'cafl': 'CAFL'
        }

        # 1. 测试准确率随轮次变化
        ax1 = axes[0, 0]
        for algorithm, results in self.results.items():
            if results['test_accuracies']:
                ax1.plot(results['communication_rounds'], results['test_accuracies'],
                        label=algorithm_names.get(algorithm, algorithm.upper()),
                        color=colors.get(algorithm, 'black'),
                        linewidth=2, marker='o', markersize=4)

        ax1.set_xlabel('通信轮次')
        ax1.set_ylabel('测试准确率')
        ax1.set_title('测试准确率收敛曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 训练损失随轮次变化
        ax2 = axes[0, 1]
        for algorithm, results in self.results.items():
            if results['train_losses']:
                ax2.plot(results['communication_rounds'], results['train_losses'],
                        label=algorithm_names.get(algorithm, algorithm.upper()),
                        color=colors.get(algorithm, 'black'),
                        linewidth=2, marker='s', markersize=4)

        ax2.set_xlabel('通信轮次')
        ax2.set_ylabel('平均训练损失')
        ax2.set_title('训练损失变化曲线')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 最终性能对比（柱状图）
        ax3 = axes[1, 0]
        algorithms = []
        final_accuracies = []

        for algorithm, results in self.results.items():
            if results['test_accuracies']:
                algorithms.append(algorithm_names.get(algorithm, algorithm.upper()))
                final_accuracies.append(results['test_accuracies'][-1])

        bars = ax3.bar(algorithms, final_accuracies,
                      color=[colors.get(alg.lower().replace('-', '_'), 'gray') for alg in algorithms])
        ax3.set_ylabel('最终测试准确率')
        ax3.set_title('最终性能对比')
        ax3.grid(True, alpha=0.3, axis='y')

        # 在柱状图上添加数值标签
        for bar, acc in zip(bars, final_accuracies):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

        # 4. 训练时间对比
        ax4 = axes[1, 1]
        training_times = []

        for algorithm in algorithms:
            alg_key = algorithm.lower().replace('-', '_')
            if alg_key in self.results:
                training_times.append(self.results[alg_key].get('training_time', 0))
            else:
                training_times.append(0)

        bars = ax4.bar(algorithms, training_times,
                      color=[colors.get(alg.lower().replace('-', '_'), 'gray') for alg in algorithms])
        ax4.set_ylabel('训练时间 (秒)')
        ax4.set_title('训练时间对比')
        ax4.grid(True, alpha=0.3, axis='y')

        # 在柱状图上添加数值标签
        for bar, time_val in zip(bars, training_times):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(training_times)*0.01,
                    f'{time_val:.1f}s', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图片
        if self.config.save_results:
            plot_path = os.path.join(self.results_dir, 'comparison_results.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"结果图已保存到: {plot_path}")

        plt.show()

    def save_experiment_results(self):
        """保存实验结果到文件"""
        print(f"\n保存实验结果到: {self.results_dir}")

        # 保存配置信息
        config_dict = {
            'dataset': self.config.dataset,
            'model': self.config.model,
            'num_clients': self.config.num_clients,
            'num_rounds': self.config.num_rounds,
            'local_epochs': self.config.local_epochs,
            'batch_size': self.config.batch_size,
            'learning_rate': self.config.learning_rate,
            'iid': self.config.iid,
            'alpha': self.config.alpha,
            'client_fraction': self.config.client_fraction,
            'algorithms': self.config.algorithms,
            'seed': self.config.seed
        }

        config_path = os.path.join(self.results_dir, 'experiment_config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

        # 保存详细结果
        results_path = os.path.join(self.results_dir, 'detailed_results.json')
        with open(results_path, 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            serializable_results = {}
            for algorithm, results in self.results.items():
                serializable_results[algorithm] = {
                    'test_accuracies': results['test_accuracies'],
                    'train_losses': results['train_losses'],
                    'communication_rounds': results['communication_rounds'],
                    'convergence_round': results['convergence_round'],
                    'training_time': results.get('training_time', 0)
                }
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        print(f"配置文件已保存到: {config_path}")
        print(f"详细结果已保存到: {results_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='联邦学习算法对比实验')
    
    # 基础参数
    parser.add_argument('--dataset', type=str, default='cifar10', 
                       choices=['mnist', 'cifar10', 'cifar100', 'fashion_mnist'],
                       help='数据集选择')
    parser.add_argument('--model', type=str, default='cnn',
                       choices=['cnn', 'resnet18', 'resnet50'],
                       help='模型选择')
    parser.add_argument('--num_clients', type=int, default=100, help='客户端总数')
    parser.add_argument('--num_rounds', type=int, default=100, help='通信轮数')
    parser.add_argument('--local_epochs', type=int, default=5, help='本地训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--lr', type=float, default=0.01, help='学习率')
    
    # 数据分布参数
    parser.add_argument('--iid', action='store_true', help='使用IID数据分布')
    parser.add_argument('--alpha', type=float, default=0.2, help='Dirichlet分布参数')
    
    # 算法选择
    parser.add_argument('--algorithms', nargs='+', 
                       default=['fedavg', 'appafl', 'swim_appafl', 'cafl'],
                       choices=['fedavg', 'appafl', 'swim_appafl', 'cafl'],
                       help='要运行的算法列表')
    
    # 其他参数
    parser.add_argument('--device', type=str, default='auto', help='计算设备')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--save_results', action='store_true', help='保存实验结果')
    parser.add_argument('--plot_results', action='store_true', help='绘制结果图')
    
    args = parser.parse_args()
    
    # 创建配置
    config = ExperimentConfig()
    
    # 更新配置
    for key, value in vars(args).items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    # 自动选择设备
    if config.device == 'auto':
        config.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 运行实验
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()


if __name__ == '__main__':
    main()
