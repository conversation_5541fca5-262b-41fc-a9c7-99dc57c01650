'''
ResNet在PyTorch中的实现，专门适配CIFAR数据集
用于SWIM联邦学习框架

参考文献:
[1] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
    Deep Residual Learning for Image Recognition. arXiv:1512.03385
'''

import torch
import torch.nn as nn

def conv3x3(in_planes, out_planes, stride=1, groups=1, dilation=1):
    """
    3x3卷积层（带填充）

    Args:
        in_planes: 输入通道数
        out_planes: 输出通道数
        stride: 步长
        groups: 分组卷积的组数
        dilation: 膨胀率

    Returns:
        nn.Conv2d: 3x3卷积层
    """
    return nn.Conv2d(in_planes, out_planes, kernel_size=3, stride=stride,
                     padding=dilation, groups=groups, bias=False, dilation=dilation)


def conv1x1(in_planes, out_planes, stride=1):
    """
    1x1卷积层

    Args:
        in_planes: 输入通道数
        out_planes: 输出通道数
        stride: 步长

    Returns:
        nn.Conv2d: 1x1卷积层
    """
    return nn.Conv2d(in_planes, out_planes, kernel_size=1, stride=stride, bias=False)


class BasicBlock(nn.Module):
    """
    ResNet基础块
    包含两个3x3卷积层和残差连接
    """
    expansion = 1  # 输出通道数相对于输入通道数的扩展倍数

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        """
        初始化基础块

        Args:
            inplanes: 输入通道数
            planes: 输出通道数
            stride: 步长
            downsample: 下采样层（用于残差连接）
            groups: 分组卷积的组数
            base_width: 基础宽度
            dilation: 膨胀率
            norm_layer: 归一化层类型
        """
        super(BasicBlock, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if groups != 1 or base_width != 64:
            raise ValueError('BasicBlock only supports groups=1 and base_width=64')
        if dilation > 1:
            raise NotImplementedError("Dilation > 1 not supported in BasicBlock")

        # 第一个卷积层（可能包含下采样）
        self.conv1 = conv3x3(inplanes, planes, stride)
        self.bn1 = norm_layer(planes)
        self.relu = nn.ReLU(inplace=True)

        # 第二个卷积层
        self.conv2 = conv3x3(planes, planes)
        self.bn2 = norm_layer(planes)

        # 下采样层（用于匹配残差连接的维度）
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入张量

        Returns:
            out: 输出张量
        """
        identity = x  # 保存输入用于残差连接

        # 第一个卷积块
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        # 第二个卷积块
        out = self.conv2(out)
        out = self.bn2(out)

        # 如果需要下采样，对identity进行变换
        if self.downsample is not None:
            identity = self.downsample(x)

        # 残差连接
        out += identity
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    # Bottleneck in torchvision places the stride for downsampling at 3x3 convolution(self.conv2)
    # while original implementation places the stride at the first 1x1 convolution(self.conv1)
    # according to "Deep residual learning for image recognition"https://arxiv.org/abs/1512.03385.
    # This variant is also known as ResNet V1.5 and improves accuracy according to
    # https://ngc.nvidia.com/catalog/model-scripts/nvidia:resnet_50_v1_5_for_pytorch.

    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(Bottleneck, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.)) * groups
        # Both self.conv2 and self.downsample layers downsample the input when stride != 1
        self.conv1 = conv1x1(inplanes, width)
        self.bn1 = norm_layer(width)
        self.conv2 = conv3x3(width, width, stride, groups, dilation)
        self.bn2 = norm_layer(width)
        self.conv3 = conv1x1(width, planes * self.expansion)
        self.bn3 = norm_layer(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class ResNetCifar10(nn.Module):
    """
    适配CIFAR数据集的ResNet模型
    相比标准ResNet，第一层卷积使用3x3卷积且步长为1，适合32x32的小图像
    """

    def __init__(self, block, layers, num_classes=1000, zero_init_residual=False,
                 groups=1, width_per_group=64, replace_stride_with_dilation=None,
                 norm_layer=None):
        """
        初始化ResNet模型

        Args:
            block: 残差块类型（BasicBlock或Bottleneck）
            layers: 每个阶段的块数量列表
            num_classes: 分类数量
            zero_init_residual: 是否对残差分支的最后一个BN层进行零初始化
            groups: 分组卷积的组数
            width_per_group: 每组的宽度
            replace_stride_with_dilation: 是否用膨胀卷积替换步长
            norm_layer: 归一化层类型
        """
        super(ResNetCifar10, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        self._norm_layer = norm_layer

        self.inplanes = 64  # 初始通道数
        self.dilation = 1   # 初始膨胀率

        # 配置膨胀卷积选项
        if replace_stride_with_dilation is None:
            # 每个元素表示是否用膨胀卷积替换2x2步长
            replace_stride_with_dilation = [False, False, False]
        if len(replace_stride_with_dilation) != 3:
            raise ValueError("replace_stride_with_dilation should be None "
                             "or a 3-element tuple, got {}".format(replace_stride_with_dilation))

        self.groups = groups
        self.base_width = width_per_group

        # 第一层卷积（适配CIFAR的32x32图像，使用3x3卷积，步长1）
        self.conv1 = nn.Conv2d(3, self.inplanes, kernel_size=3, stride=1, padding=1,
                               bias=False)
        self.bn1 = norm_layer(self.inplanes)
        self.relu = nn.ReLU(inplace=True)

        # 四个残差层
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2,
                                       dilate=replace_stride_with_dilation[0])
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2,
                                       dilate=replace_stride_with_dilation[1])
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2,
                                       dilate=replace_stride_with_dilation[2])

        # 全局平均池化和分类层
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

        # Zero-initialize the last BN in each residual branch,
        # so that the residual branch starts with zeros, and each residual block behaves like an identity.
        # This improves the model by 0.2~0.3% according to https://arxiv.org/abs/1706.02677
        if zero_init_residual:
            for m in self.modules():
                if isinstance(m, Bottleneck):
                    nn.init.constant_(m.bn3.weight, 0)
                elif isinstance(m, BasicBlock):
                    nn.init.constant_(m.bn2.weight, 0)

    def _make_layer(self, block, planes, blocks, stride=1, dilate=False):
        norm_layer = self._norm_layer
        downsample = None
        previous_dilation = self.dilation
        if dilate:
            self.dilation *= stride
            stride = 1
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                conv1x1(self.inplanes, planes * block.expansion, stride),
                norm_layer(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample, self.groups,
                            self.base_width, previous_dilation, norm_layer))
        self.inplanes = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, groups=self.groups,
                                base_width=self.base_width, dilation=self.dilation,
                                norm_layer=norm_layer))

        return nn.Sequential(*layers)

    def _forward_impl(self, x):
        # See note [TorchScript super()]
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)

        return x

    def forward(self, x):
        return self._forward_impl(x)


def ResNet18_cifar10(**kwargs):
    """
    创建适配CIFAR数据集的ResNet-18模型

    参考文献:
    "Deep Residual Learning for Image Recognition" <https://arxiv.org/pdf/1512.03385.pdf>

    Args:
        **kwargs: 传递给ResNetCifar10的其他参数

    Returns:
        ResNetCifar10: ResNet-18模型实例
    """
    return ResNetCifar10(BasicBlock, [2, 2, 2, 2], **kwargs)


def ResNet50_cifar10(**kwargs):
    """
    创建适配CIFAR数据集的ResNet-50模型

    参考文献:
    "Deep Residual Learning for Image Recognition" <https://arxiv.org/pdf/1512.03385.pdf>

    Args:
        **kwargs: 传递给ResNetCifar10的其他参数

    Returns:
        ResNetCifar10: ResNet-50模型实例
    """
    return ResNetCifar10(Bottleneck, [3, 4, 6, 3], **kwargs)