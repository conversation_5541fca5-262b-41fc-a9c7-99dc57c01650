# SWIM联邦学习工具函数模块
# 包含数据处理、模型训练、准确率计算等辅助功能

import os
import logging
import numpy as np
import torch
import torchvision.transforms as transforms
import torch.utils.data as data
from torch.autograd import Variable
import torch.nn.functional as F
import torch.nn as nn
import random
from sklearn.metrics import confusion_matrix

from model import *
from datasets import CIFAR10_truncated, CIFAR100_truncated, ImageFolder_custom

# 配置日志系统
logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def mkdirs(dirpath):
    """
    创建目录（如果不存在）

    Args:
        dirpath: 要创建的目录路径
    """
    try:
        os.makedirs(dirpath)
    except Exception as _:
        pass



def load_cifar10_data(datadir):
    """
    加载CIFAR-10数据集

    Args:
        datadir: 数据目录路径

    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建CIFAR-10数据集对象
    cifar10_train_ds = CIFAR10_truncated(datadir, train=True, download=True, transform=transform)
    cifar10_test_ds = CIFAR10_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = cifar10_train_ds.data, cifar10_train_ds.target
    X_test, y_test = cifar10_test_ds.data, cifar10_test_ds.target

    return (X_train, y_train, X_test, y_test)


def load_cifar100_data(datadir):
    """
    加载CIFAR-100数据集

    Args:
        datadir: 数据目录路径

    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建CIFAR-100数据集对象
    cifar100_train_ds = CIFAR100_truncated(datadir, train=True, download=True, transform=transform)
    cifar100_test_ds = CIFAR100_truncated(datadir, train=False, download=True, transform=transform)

    # 提取数据和标签
    X_train, y_train = cifar100_train_ds.data, cifar100_train_ds.target
    X_test, y_test = cifar100_test_ds.data, cifar100_test_ds.target

    return (X_train, y_train, X_test, y_test)


def load_tinyimagenet_data(datadir):
    """
    加载Tiny ImageNet数据集

    Args:
        datadir: 数据目录路径

    Returns:
        tuple: (X_train, y_train, X_test, y_test) 训练和测试数据
    """
    # 定义基本的数据变换（仅转换为张量）
    transform = transforms.Compose([transforms.ToTensor()])

    # 创建自定义图像文件夹数据集对象
    xray_train_ds = ImageFolder_custom(datadir+'train/', transform=transform)
    xray_test_ds = ImageFolder_custom(datadir+'val/', transform=transform)

    # 提取文件路径和标签
    X_train, y_train = np.array([s[0] for s in xray_train_ds.samples]), np.array([int(s[1]) for s in xray_train_ds.samples])
    X_test, y_test = np.array([s[0] for s in xray_test_ds.samples]), np.array([int(s[1]) for s in xray_test_ds.samples])

    return (X_train, y_train, X_test, y_test)


def record_net_data_stats(y_train, net_dataidx_map, logdir):
    """
    记录每个客户端的数据统计信息

    Args:
        y_train: 训练数据标签
        net_dataidx_map: 网络数据索引映射（客户端ID -> 数据索引列表）
        logdir: 日志目录

    Returns:
        net_cls_counts: 每个客户端的类别数据统计
    """
    net_cls_counts = {}

    # 统计每个客户端的类别分布
    for net_i, dataidx in net_dataidx_map.items():
        # 获取当前客户端数据的唯一类别和对应数量
        unq, unq_cnt = np.unique(y_train[dataidx], return_counts=True)
        # 创建类别到数量的映射
        tmp = {unq[i]: unq_cnt[i] for i in range(len(unq))}
        net_cls_counts[net_i] = tmp

    # 计算每个客户端的总数据量
    data_list = []
    for net_id, data in net_cls_counts.items():
        n_total = 0
        for class_id, n_data in data.items():
            n_total += n_data
        data_list.append(n_total)

    # 打印数据分布的统计信息
    print('mean:', np.mean(data_list))  # 平均数据量
    print('std:', np.std(data_list))    # 数据量标准差
    logger.info('Data statistics: %s' % str(net_cls_counts))

    return net_cls_counts


def partition_data(dataset, datadir, logdir, partition, n_parties, beta=0.4):
    """
    将数据分割给不同的客户端
    支持同质（IID）和异质（Non-IID）数据分布

    Args:
        dataset: 数据集名称
        datadir: 数据目录
        logdir: 日志目录
        partition: 分割策略 ("homo"/"iid" 或 "noniid"/"noniid-labeldir")
        n_parties: 客户端数量
        beta: 狄利克雷分布参数（控制数据异质性程度）

    Returns:
        tuple: (X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts)
    """
    # 根据数据集类型加载相应数据
    if dataset == 'cifar10':
        X_train, y_train, X_test, y_test = load_cifar10_data(datadir)
    elif dataset == 'cifar100':
        X_train, y_train, X_test, y_test = load_cifar100_data(datadir)
    elif dataset == 'tinyimagenet':
        X_train, y_train, X_test, y_test = load_tinyimagenet_data(datadir)

    n_train = y_train.shape[0]  # 训练样本总数
    net_dataidx_map = {}  # 客户端数据索引映射

    # 同质数据分布（IID）：随机均匀分配数据
    if partition == "homo" or partition == "iid":
        # 随机打乱所有训练数据索引
        idxs = np.random.permutation(n_train)
        # 将索引均匀分配给各个客户端
        batch_idxs = np.array_split(idxs, n_parties)
        net_dataidx_map = {i: batch_idxs[i] for i in range(n_parties)}

    # 异质数据分布（Non-IID）：使用狄利克雷分布控制数据异质性
    elif partition == "noniid-labeldir" or partition == "noniid":
        min_size = 0  # 当前最小客户端数据量
        min_require_size = 10  # 要求的最小数据量

        # 根据数据集确定类别数
        K = 10  # 默认类别数
        if dataset == 'cifar100':
            K = 100
        elif dataset == 'tinyimagenet':
            K = 200

        N = y_train.shape[0]  # 总样本数

        # 重复分配直到每个客户端都有足够的数据
        idx_batch = [[] for _ in range(n_parties)]
        while min_size < min_require_size:
            idx_batch = [[] for _ in range(n_parties)]

            # 对每个类别进行分配
            for k in range(K):
                # 获取当前类别的所有样本索引
                idx_k = np.where(y_train == k)[0]
                np.random.shuffle(idx_k)

                # 使用狄利克雷分布生成分配比例
                proportions = np.random.dirichlet(np.repeat(beta, n_parties))

                # 确保数据分配的平衡性
                proportions = np.array([p * (len(idx_j) < N / n_parties) for p, idx_j in zip(proportions, idx_batch)])
                proportions = proportions / proportions.sum()

                # 计算分割点
                proportions = (np.cumsum(proportions) * len(idx_k)).astype(int)[:-1]

                # 将当前类别的数据分配给各个客户端
                idx_batch = [idx_j + idx.tolist() for idx_j, idx in zip(idx_batch, np.split(idx_k, proportions))]

                # 更新最小数据量
                min_size = min([len(idx_j) for idx_j in idx_batch])

        # 为每个客户端的数据进行随机打乱
        for j in range(n_parties):
            np.random.shuffle(idx_batch[j])
            net_dataidx_map[j] = idx_batch[j]

    # 记录数据分布统计信息
    traindata_cls_counts = record_net_data_stats(y_train, net_dataidx_map, logdir)
    return (X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts)


def get_trainable_parameters(net, device='cpu'):
    """
    将网络的可训练参数提取为一个向量

    Args:
        net: 神经网络模型
        device: 运行设备

    Returns:
        X: 包含所有可训练参数的一维张量
    """
    # 过滤出需要梯度的参数（可训练参数）
    trainable = filter(lambda p: p.requires_grad, net.parameters())
    paramlist = list(trainable)

    # 计算总参数数量
    N = 0
    for params in paramlist:
        N += params.numel()  # 获取参数张量中元素的总数

    # 创建用于存储所有参数的向量
    X = torch.empty(N, dtype=torch.float64, device=device)
    X.fill_(0.0)

    # 将所有参数复制到向量中
    offset = 0
    for params in paramlist:
        numel = params.numel()
        with torch.no_grad():
            # 将参数展平并复制到向量的相应位置
            X[offset:offset + numel].copy_(params.data.view_as(X[offset:offset + numel].data))
        offset += numel

    return X


def put_trainable_parameters(net, X):
    """
    将参数向量的值设置回网络的可训练参数

    Args:
        net: 神经网络模型
        X: 包含参数值的一维张量
    """
    # 过滤出需要梯度的参数（可训练参数）
    trainable = filter(lambda p: p.requires_grad, net.parameters())
    paramlist = list(trainable)

    # 将向量中的值复制回网络参数
    offset = 0
    for params in paramlist:
        numel = params.numel()
        with torch.no_grad():
            # 将向量中的相应部分重塑为参数的原始形状并复制
            params.data.copy_(X[offset:offset + numel].data.view_as(params.data))
        offset += numel


def compute_accuracy(model, dataloader, get_confusion_matrix=False, device="cpu", multiloader=False):
    """
    计算模型在给定数据集上的准确率和损失

    Args:
        model: 要评估的模型
        dataloader: 数据加载器或数据加载器列表
        get_confusion_matrix: 是否计算混淆矩阵
        device: 运行设备
        multiloader: 是否使用多个数据加载器

    Returns:
        如果get_confusion_matrix=True: (accuracy, confusion_matrix, avg_loss)
        否则: (accuracy, avg_loss)
    """
    # 记录模型原始状态
    was_training = False
    if model.training:
        model.eval()  # 设置为评估模式
        was_training = True

    # 初始化标签列表
    true_labels_list, pred_labels_list = np.array([]), np.array([])

    correct, total = 0, 0  # 正确预测数和总样本数

    # 根据设备选择损失函数
    if device == 'cpu':
        criterion = nn.CrossEntropyLoss()
    elif "cuda" in device.type:
        criterion = nn.CrossEntropyLoss().cuda()

    loss_collector = []  # 收集损失值
    # 处理多个数据加载器的情况
    if multiloader:
        for loader in dataloader:
            with torch.no_grad():  # 禁用梯度计算
                for batch_idx, (x, target) in enumerate(loader):
                    # 将数据移动到指定设备
                    if device != 'cpu':
                        x, target = x.cuda(), target.to(dtype=torch.int64).cuda()

                    # 前向传播
                    _, _, out = model(x)

                    # 计算损失
                    loss = criterion(out, target)

                    # 获取预测标签
                    _, pred_label = torch.max(out.data, 1)

                    # 更新统计信息
                    loss_collector.append(loss.item())
                    total += x.data.size()[0]
                    correct += (pred_label == target.data).sum().item()

                    # 收集真实标签和预测标签
                    if device == "cpu":
                        pred_labels_list = np.append(pred_labels_list, pred_label.numpy())
                        true_labels_list = np.append(true_labels_list, target.data.numpy())
                    else:
                        pred_labels_list = np.append(pred_labels_list, pred_label.cpu().numpy())
                        true_labels_list = np.append(true_labels_list, target.data.cpu().numpy())
        avg_loss = sum(loss_collector) / len(loss_collector)
    # 处理单个数据加载器的情况
    else:
        with torch.no_grad():  # 禁用梯度计算
            for batch_idx, (x, target) in enumerate(dataloader):
                # 将数据移动到指定设备
                if device != 'cpu':
                    x, target = x.cuda(), target.to(dtype=torch.int64).cuda()

                # 前向传播
                _, _, out = model(x)

                # 计算损失
                loss = criterion(out, target)

                # 获取预测标签
                _, pred_label = torch.max(out.data, 1)

                # 更新统计信息
                loss_collector.append(loss.item())
                total += x.data.size()[0]
                correct += (pred_label == target.data).sum().item()

                # 收集真实标签和预测标签
                if device == "cpu":
                    pred_labels_list = np.append(pred_labels_list, pred_label.numpy())
                    true_labels_list = np.append(true_labels_list, target.data.numpy())
                else:
                    pred_labels_list = np.append(pred_labels_list, pred_label.cpu().numpy())
                    true_labels_list = np.append(true_labels_list, target.data.cpu().numpy())
            avg_loss = sum(loss_collector) / len(loss_collector)

    # 如果需要，计算混淆矩阵
    if get_confusion_matrix:
        conf_matrix = confusion_matrix(true_labels_list, pred_labels_list)

    # 恢复模型原始状态
    if was_training:
        model.train()

    # 返回结果
    if get_confusion_matrix:
        return correct / float(total), conf_matrix, avg_loss

    return correct / float(total), avg_loss

def compute_loss(model, dataloader, device="cpu"):
    """
    计算模型在给定数据集上的平均损失

    Args:
        model: 要评估的模型
        dataloader: 数据加载器
        device: 运行设备

    Returns:
        avg_loss: 平均损失值
    """
    # 记录模型原始状态
    was_training = False
    if model.training:
        model.eval()  # 设置为评估模式
        was_training = True

    # 根据设备选择损失函数
    if device == 'cpu':
        criterion = nn.CrossEntropyLoss()
    elif "cuda" in device.type:
        criterion = nn.CrossEntropyLoss().cuda()

    loss_collector = []  # 收集损失值
    with torch.no_grad():  # 禁用梯度计算
        for batch_idx, (x, target) in enumerate(dataloader):
            # 将数据移动到指定设备
            if device != 'cpu':
                x, target = x.cuda(), target.to(dtype=torch.int64).cuda()

            # 前向传播
            _, _, out = model(x)

            # 计算损失
            loss = criterion(out, target)
            loss_collector.append(loss.item())

        # 计算平均损失
        avg_loss = sum(loss_collector) / len(loss_collector)

    # 恢复模型原始状态
    if was_training:
        model.train()

    return avg_loss


def save_model(model, model_index, args):
    """
    保存模型到文件

    Args:
        model: 要保存的模型
        model_index: 模型索引
        args: 包含模型目录的参数
    """
    logger.info("saving local model-{}".format(model_index))
    with open(args.modeldir + "trained_local_model" + str(model_index), "wb") as f_:
        torch.save(model.state_dict(), f_)
    return


def load_model(model, model_index, device="cpu"):
    """
    从文件加载模型

    Args:
        model: 要加载参数的模型
        model_index: 模型索引
        device: 运行设备

    Returns:
        model: 加载参数后的模型
    """
    with open("trained_local_model" + str(model_index), "rb") as f_:
        model.load_state_dict(torch.load(f_))

    # 将模型移动到指定设备
    if device == "cpu":
        model.to(device)
    else:
        model.cuda()
    return model


def get_dataloader(dataset, datadir, train_bs, test_bs, dataidxs=None, noise_level=0):
    """
    创建数据加载器

    Args:
        dataset: 数据集名称
        datadir: 数据目录
        train_bs: 训练批次大小
        test_bs: 测试批次大小
        dataidxs: 数据索引（用于客户端数据分割）
        noise_level: 噪声级别

    Returns:
        tuple: (train_dl, test_dl, train_ds, test_ds) 训练和测试的数据加载器及数据集
    """
    # 处理CIFAR数据集
    if dataset in ('cifar10', 'cifar100'):
        if dataset == 'cifar10':
            dl_obj = CIFAR10_truncated

            # CIFAR-10的数据标准化参数
            normalize = transforms.Normalize(mean=[x / 255.0 for x in [125.3, 123.0, 113.9]],
                                             std=[x / 255.0 for x in [63.0, 62.1, 66.7]])

            # 训练数据变换（包含数据增强）
            transform_train = transforms.Compose([
                transforms.ToTensor(),
                transforms.Lambda(lambda x: F.pad(
                    Variable(x.unsqueeze(0), requires_grad=False),
                    (4, 4, 4, 4), mode='reflect').data.squeeze()),  # 反射填充
                transforms.ToPILImage(),
                transforms.ColorJitter(brightness=noise_level),  # 亮度抖动
                transforms.RandomCrop(32),  # 随机裁剪
                transforms.RandomHorizontalFlip(),  # 随机水平翻转
                transforms.ToTensor(),
                normalize
            ])

            # 测试数据变换（仅标准化）
            transform_test = transforms.Compose([
                transforms.ToTensor(),
                normalize])

        elif dataset == 'cifar100':
            dl_obj = CIFAR100_truncated

            # CIFAR-100的数据标准化参数
            normalize = transforms.Normalize(mean=[0.5070751592371323, 0.48654887331495095, 0.4409178433670343],
                                             std=[0.2673342858792401, 0.2564384629170883, 0.27615047132568404])

            # 训练数据变换（包含数据增强）
            transform_train = transforms.Compose([
                transforms.RandomCrop(32, padding=4),  # 随机裁剪（带填充）
                transforms.RandomHorizontalFlip(),     # 随机水平翻转
                transforms.RandomRotation(15),         # 随机旋转
                transforms.ToTensor(),
                normalize
            ])

            # 测试数据变换（仅标准化）
            transform_test = transforms.Compose([
                transforms.ToTensor(),
                normalize])

        # 创建数据集对象
        train_ds = dl_obj(datadir, dataidxs=dataidxs, train=True, transform=transform_train, download=True)
        test_ds = dl_obj(datadir, train=False, transform=transform_test, download=True)

        # 创建数据加载器
        train_dl = data.DataLoader(dataset=train_ds, batch_size=train_bs, drop_last=True, shuffle=True)
        test_dl = data.DataLoader(dataset=test_ds, batch_size=test_bs, shuffle=False)

    # 处理Tiny ImageNet数据集
    elif dataset == 'tinyimagenet':
        dl_obj = ImageFolder_custom

        # 训练数据变换
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),  # 标准化到[-1, 1]
        ])

        # 测试数据变换
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),  # 标准化到[-1, 1]
        ])

        # 创建数据集对象
        train_ds = dl_obj(datadir+'./train/', dataidxs=dataidxs, transform=transform_train)
        test_ds = dl_obj(datadir+'./val/', transform=transform_test)

        # 创建数据加载器
        train_dl = data.DataLoader(dataset=train_ds, batch_size=train_bs, drop_last=True, shuffle=True)
        test_dl = data.DataLoader(dataset=test_ds, batch_size=test_bs, shuffle=False)

    return train_dl, test_dl, train_ds, test_ds
