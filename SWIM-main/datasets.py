# SWIM联邦学习数据集模块
# 包含自定义数据集类，支持数据分割和联邦学习场景

import torch.utils.data as data
from PIL import Image
import numpy as np
import torchvision
from torchvision.datasets import MNIST, EMNIST, CIFAR10, CIFAR100, SVHN, FashionMNIST, ImageFolder, DatasetFolder, utils

import os
import os.path
import logging

# 配置日志系统
logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 支持的图像文件扩展名
IMG_EXTENSIONS = ('.jpg', '.jpeg', '.png', '.ppm', '.bmp', '.pgm', '.tif', '.tiff', '.webp')


def mkdirs(dirpath):
    """
    创建目录（如果不存在）

    Args:
        dirpath: 要创建的目录路径
    """
    try:
        os.makedirs(dirpath)
    except Exception as _:
        pass



class CIFAR10_truncated(data.Dataset):
    """
    CIFAR-10截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """

    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化CIFAR-10截断数据集

        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集

        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始CIFAR-10数据集
        cifar_dataobj = CIFAR10(self.root, self.train, self.transform, self.target_transform, self.download)

        # 兼容不同版本的torchvision
        if torchvision.__version__ == '0.2.1':
            if self.train:
                data, target = cifar_dataobj.train_data, np.array(cifar_dataobj.train_labels)
            else:
                data, target = cifar_dataobj.test_data, np.array(cifar_dataobj.test_labels)
        else:
            data = cifar_dataobj.data
            target = np.array(cifar_dataobj.targets)

        # 如果指定了数据索引，则只保留相应的数据
        if self.dataidxs is not None:
            data = data[self.dataidxs]
            target = target[self.dataidxs]

        return data, target

    def truncate_channel(self, index):
        """
        截断指定索引数据的颜色通道（将绿色和蓝色通道设为0）

        Args:
            index: 要截断的数据索引数组
        """
        for i in range(index.shape[0]):
            gs_index = index[i]
            self.data[gs_index, :, :, 1] = 0.0  # 绿色通道设为0
            self.data[gs_index, :, :, 2] = 0.0  # 蓝色通道设为0

    def __getitem__(self, index):
        """
        获取指定索引的数据项

        Args:
            index (int): 数据索引

        Returns:
            tuple: (image, target) 图像和对应的标签
        """
        img, target = self.data[index], self.target[index]

        # 应用数据变换
        if self.transform is not None:
            img = self.transform(img)

        # 应用标签变换
        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        """
        返回数据集大小

        Returns:
            int: 数据集中样本的数量
        """
        return len(self.data)


class CIFAR100_truncated(data.Dataset):
    """
    CIFAR-100截断数据集类
    支持根据指定的数据索引创建子数据集，用于联邦学习中的数据分割
    """

    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None, download=False):
        """
        初始化CIFAR-100截断数据集

        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集
            transform: 数据变换
            target_transform: 标签变换
            download: 是否下载数据
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform
        self.download = download

        # 构建截断后的数据集
        self.data, self.target = self.__build_truncated_dataset__()

    def __build_truncated_dataset__(self):
        """
        构建截断数据集

        Returns:
            tuple: (data, target) 截断后的数据和标签
        """
        # 加载原始CIFAR-100数据集
        cifar_dataobj = CIFAR100(self.root, self.train, self.transform, self.target_transform, self.download)

        # 兼容不同版本的torchvision
        if torchvision.__version__ == '0.2.1':
            if self.train:
                data, target = cifar_dataobj.train_data, np.array(cifar_dataobj.train_labels)
            else:
                data, target = cifar_dataobj.test_data, np.array(cifar_dataobj.test_labels)
        else:
            data = cifar_dataobj.data
            target = np.array(cifar_dataobj.targets)

        # 如果指定了数据索引，则只保留相应的数据
        if self.dataidxs is not None:
            data = data[self.dataidxs]
            target = target[self.dataidxs]

        return data, target

    def __getitem__(self, index):
        """
        获取指定索引的数据项

        Args:
            index (int): 数据索引

        Returns:
            tuple: (image, target) 图像和对应的标签
        """
        img, target = self.data[index], self.target[index]
        img = Image.fromarray(img)  # 转换为PIL图像

        # 应用数据变换
        if self.transform is not None:
            img = self.transform(img)

        # 应用标签变换
        if self.target_transform is not None:
            target = self.target_transform(target)

        return img, target

    def __len__(self):
        """
        返回数据集大小

        Returns:
            int: 数据集中样本的数量
        """
        return len(self.data)




class ImageFolder_custom(DatasetFolder):
    """
    自定义图像文件夹数据集类
    支持根据指定的数据索引创建子数据集，用于处理文件夹结构的图像数据
    主要用于Tiny ImageNet等数据集
    """

    def __init__(self, root, dataidxs=None, train=True, transform=None, target_transform=None):
        """
        初始化自定义图像文件夹数据集

        Args:
            root: 数据根目录
            dataidxs: 数据索引列表（如果为None则使用全部数据）
            train: 是否为训练集（保持接口一致性）
            transform: 数据变换
            target_transform: 标签变换
        """
        self.root = root
        self.dataidxs = dataidxs
        self.train = train
        self.transform = transform
        self.target_transform = target_transform

        # 创建ImageFolder对象并获取样本信息
        imagefolder_obj = ImageFolder(self.root, self.transform, self.target_transform)
        self.loader = imagefolder_obj.loader  # 图像加载器

        # 根据数据索引筛选样本
        if self.dataidxs is not None:
            self.samples = np.array(imagefolder_obj.samples)[self.dataidxs]
        else:
            self.samples = np.array(imagefolder_obj.samples)

    def __getitem__(self, index):
        """
        获取指定索引的数据项

        Args:
            index: 数据索引

        Returns:
            tuple: (sample, target) 图像样本和对应的标签
        """
        # 获取文件路径和标签
        path = self.samples[index][0]
        target = self.samples[index][1]
        target = int(target)

        # 加载图像
        sample = self.loader(path)

        # 应用数据变换
        if self.transform is not None:
            sample = self.transform(sample)

        # 应用标签变换
        if self.target_transform is not None:
            target = self.target_transform(target)

        return sample, target

    def __len__(self):
        """
        返回数据集大小

        Returns:
            int: 数据集中样本的数量
        """
        if self.dataidxs is None:
            return len(self.samples)
        else:
            return len(self.dataidxs)
