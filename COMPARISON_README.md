# 联邦学习算法综合对比试验系统

## 概述

本系统实现了四种联邦学习算法的综合对比试验：

1. **FedAvg** - 标准联邦平均算法（基准）
2. **APPAFL** - 异步个性化隐私保护联邦学习
3. **SWIM-APPAFL** - SWIM对比学习与APPAFL的融合算法
4. **CAFL** - 基于密度峰值聚类的异步联邦学习

## 系统特点

### 🔧 技术特性
- **统一模型架构**: 使用SWIM-APPAFL中的模型定义，确保公平对比
- **多数据集支持**: CIFAR-10、CIFAR-100、MNIST、Fashion-MNIST
- **非IID数据分布**: 支持Dirichlet分布的非IID数据分区
- **异步联邦学习**: 支持客户端延迟和陈旧模型处理
- **对比学习**: 集成SWIM算法的对比学习机制

### 📊 评估指标
- 测试准确率收敛曲线
- 训练损失变化
- 收敛速度分析
- 训练时间对比
- 聚类效果分析（CAFL）

### 🎨 可视化功能
- 多算法性能对比图
- 收敛曲线可视化
- 性能指标柱状图
- 结果自动保存

## 文件结构

```
.
├── federated_learning_comparison.py    # 主要对比试验实现
├── run_comparison_experiment.py        # 实验运行脚本
├── COMPARISON_README.md                # 本说明文件
├── SWIM-APPAFL-Integration/            # SWIM-APPAFL集成模块
│   ├── models.py                       # 模型定义
│   ├── swim_models.py                  # SWIM模型定义
│   ├── clients.py                      # 客户端实现
│   ├── server.py                       # 服务器实现
│   └── datasets.py                     # 数据集处理
├── APPAFL-main/                        # APPAFL原始实现
├── SWIM-main/                          # SWIM原始实现
└── 2021ZhangAFLDCS-master/             # CAFL原始实现
```

## 快速开始

### 1. 环境准备

确保已安装以下依赖：
```bash
pip install torch torchvision matplotlib seaborn numpy
```

### 2. 快速测试

运行快速测试验证系统正常工作：
```bash
python run_comparison_experiment.py --experiment quick_test
```

### 3. 运行完整实验

#### CIFAR-10数据集对比
```bash
python run_comparison_experiment.py --experiment cifar10
```

#### MNIST数据集对比
```bash
python run_comparison_experiment.py --experiment mnist
```

#### 全面对比试验
```bash
python run_comparison_experiment.py --experiment comprehensive
```

### 4. 指定GPU
```bash
python run_comparison_experiment.py --experiment cifar10 --gpu 0
```

## 实验配置

### 主要参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `dataset` | 数据集选择 | 'cifar10' |
| `model` | 模型架构 | 'cnn' |
| `num_clients` | 客户端总数 | 100 |
| `num_rounds` | 通信轮数 | 100 |
| `local_epochs` | 本地训练轮数 | 5 |
| `batch_size` | 批次大小 | 32 |
| `learning_rate` | 学习率 | 0.01 |
| `client_fraction` | 每轮参与比例 | 0.1 |
| `alpha` | 非IID程度 | 0.2 |

### SWIM算法参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `temperature` | 对比学习温度 | 0.5 |
| `mu` | 动态权重参数 | 0.5 |
| `kr` | 滑动窗口比例 | 0.5 |
| `out_dim` | 投影维度 | 256 |

### APPAFL异步参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `stale_threshold` | 延迟阈值 | 4 |
| `aggregation_threshold` | 聚合阈值 | 6 |
| `percentage_of_stale` | 延迟客户端比例 | 0.4 |

## 自定义实验

### 1. 修改实验配置

```python
from federated_learning_comparison import FederatedLearningComparison, ExperimentConfig

# 创建自定义配置
config = ExperimentConfig()
config.dataset = 'cifar100'
config.num_clients = 200
config.num_rounds = 150
config.algorithms = ['fedavg', 'swim_appafl']  # 只运行部分算法

# 运行实验
experiment = FederatedLearningComparison(config)
experiment.run_experiment()
```

### 2. 添加新算法

在`federated_learning_comparison.py`中添加新的算法实现：

```python
def run_new_algorithm(self):
    """运行新算法"""
    # 实现新算法的训练逻辑
    pass
```

### 3. 自定义评估指标

在`analyze_results`方法中添加新的评估指标：

```python
def analyze_results(self):
    # 添加自定义指标计算
    pass
```

## 结果分析

### 输出文件

实验完成后会生成以下文件：
- `comparison_results_YYYYMMDD_HHMMSS/`
  - `experiment_config.json` - 实验配置
  - `detailed_results.json` - 详细结果数据
  - `comparison_results.png` - 对比结果图

### 性能指标

系统会自动计算并显示：
- **最终准确率**: 最后一轮的测试准确率
- **最高准确率**: 整个训练过程中的最高准确率
- **收敛轮次**: 达到最高准确率90%的轮次
- **平均训练损失**: 所有轮次的平均训练损失
- **训练时间**: 算法总训练时间

### 可视化图表

1. **测试准确率收敛曲线**: 显示各算法的准确率随轮次变化
2. **训练损失变化曲线**: 显示训练损失的收敛情况
3. **最终性能对比**: 柱状图对比各算法的最终性能
4. **训练时间对比**: 各算法的计算效率对比

## 算法说明

### FedAvg
- **特点**: 标准联邦平均算法，同步训练
- **适用**: 作为基准算法进行对比
- **优势**: 简单稳定，易于实现

### APPAFL
- **特点**: 异步联邦学习，支持客户端延迟
- **适用**: 网络环境不稳定的场景
- **优势**: 处理客户端异构性和延迟

### SWIM-APPAFL
- **特点**: 结合对比学习和异步联邦学习
- **适用**: 需要提升模型性能的场景
- **优势**: 利用历史模型信息提升训练效果

### CAFL
- **特点**: 基于密度峰值聚类的异步联邦学习
- **适用**: 客户端数据分布差异较大的场景
- **优势**: 通过聚类处理客户端异构性

## 注意事项

1. **内存使用**: SWIM算法需要保存历史模型，会占用较多内存
2. **计算时间**: 对比学习和聚类算法会增加计算时间
3. **GPU内存**: 建议使用16GB以上GPU内存
4. **数据集**: 首次运行会自动下载数据集

## 故障排除

### 常见问题

1. **模块导入失败**
   - 确保SWIM-APPAFL-Integration目录存在
   - 检查Python路径设置

2. **GPU内存不足**
   - 减少batch_size
   - 减少客户端数量
   - 使用CPU训练

3. **收敛效果不佳**
   - 调整学习率
   - 增加本地训练轮数
   - 检查数据分布设置

### 调试模式

启用详细日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

### 1. 添加新数据集
在`load_data_simple`方法中添加新数据集的加载逻辑

### 2. 支持新模型
在`create_model`方法中添加新模型的创建逻辑

### 3. 自定义聚合策略
在相应算法的聚合方法中实现新的聚合策略

## 贡献指南

欢迎提交Issue和Pull Request来改进本系统！

## 许可证

本项目基于原始SWIM、APPAFL、CAFL算法的实现，请遵循相应的许可证要求。
