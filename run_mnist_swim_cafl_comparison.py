#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MNIST数据集上SWIM-APPAFL vs CAFL算法对比实验
使用CNN模型进行训练
"""

import os
import sys
from federated_learning_comparison import FederatedLearningComparison, ExperimentConfig


def run_mnist_swim_cafl_comparison():
    """运行MNIST数据集上SWIM-APPAFL vs CAFL的对比实验"""
    print("=" * 70)
    print("MNIST数据集：SWIM-APPAFL vs CAFL 算法对比实验")
    print("使用CNN模型")
    print("=" * 70)
    
    # 创建实验配置
    config = ExperimentConfig()
    
    # === 基础实验参数 ===
    config.dataset = 'mnist'           # 使用MNIST数据集
    config.model = 'cnn'               # 使用CNN模型
    config.num_clients = 100           # 客户端总数
    config.num_rounds = 50             # 通信轮数
    config.local_epochs = 5            # 本地训练轮数
    config.batch_size = 32             # 批次大小
    config.learning_rate = 0.01        # 学习率
    
    # === 数据分布参数 ===
    config.iid = False                 # 使用非IID数据分布
    config.alpha = 0.2                 # Dirichlet分布参数（控制非IID程度，越小越不均匀）
    
    # === 异步联邦学习参数 ===
    config.client_fraction = 0.1       # 每轮参与的客户端比例（10%）
    config.stale_threshold = 4          # 延迟阈值
    config.aggregation_threshold = 6    # 聚合阈值
    
    # === SWIM算法参数 ===
    config.temperature = 0.5           # 对比学习温度参数
    config.mu = 0.5                    # SWIM动态权重参数
    config.kr = 0.5                    # 滑动窗口比例参数（窗口大小 = kr * total_rounds）
    config.out_dim = 256               # 投影维度
    
    # === CAFL算法参数 ===
    config.dc = -0.02                  # 密度峰值聚类参数
    config.cluster_threshold = 0.5     # 聚类阈值
    
    # === 实验控制参数 ===
    config.algorithms = ['swim_appafl', 'cafl']  # 只对比这两个算法
    config.save_results = True         # 保存实验结果
    config.plot_results = True         # 绘制结果图
    config.seed = 42                   # 随机种子，确保实验可重现
    
    # 打印实验配置
    print("实验配置:")
    print(f"  数据集: {config.dataset.upper()}")
    print(f"  模型: {config.model.upper()}")
    print(f"  客户端数量: {config.num_clients}")
    print(f"  通信轮数: {config.num_rounds}")
    print(f"  本地训练轮数: {config.local_epochs}")
    print(f"  批次大小: {config.batch_size}")
    print(f"  学习率: {config.learning_rate}")
    print(f"  数据分布: {'IID' if config.iid else 'Non-IID'} (alpha={config.alpha})")
    print(f"  参与比例: {config.client_fraction}")
    print(f"  对比算法: {', '.join([alg.upper() for alg in config.algorithms])}")
    print()
    
    # 运行实验
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()
    
    return experiment


def run_extended_comparison():
    """运行扩展对比实验（包含FedAvg和APPAFL作为基准）"""
    print("=" * 70)
    print("MNIST数据集：扩展对比实验")
    print("SWIM-APPAFL vs CAFL vs FedAvg vs APPAFL")
    print("=" * 70)
    
    config = ExperimentConfig()
    
    # 基础配置（与上面相同）
    config.dataset = 'mnist'
    config.model = 'cnn'
    config.num_clients = 100
    config.num_rounds = 50
    config.local_epochs = 5
    config.batch_size = 32
    config.learning_rate = 0.01
    config.iid = False
    config.alpha = 0.2
    config.client_fraction = 0.1
    
    # SWIM参数
    config.temperature = 0.5
    config.mu = 0.5
    config.kr = 0.5
    config.out_dim = 256
    
    # 包含所有算法进行对比
    config.algorithms = ['fedavg', 'appafl', 'swim_appafl', 'cafl']
    config.save_results = True
    config.plot_results = True
    config.seed = 42
    
    print("扩展对比实验配置:")
    print(f"  对比算法: {', '.join([alg.upper() for alg in config.algorithms])}")
    print()
    
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()
    
    return experiment


def run_parameter_sensitivity_test():
    """运行参数敏感性测试"""
    print("=" * 70)
    print("MNIST数据集：参数敏感性测试")
    print("测试不同alpha值对SWIM-APPAFL和CAFL的影响")
    print("=" * 70)
    
    alpha_values = [0.1, 0.2, 0.5, 1.0]  # 不同的非IID程度
    results_summary = {}
    
    for alpha in alpha_values:
        print(f"\n{'='*40}")
        print(f"测试 alpha = {alpha}")
        print(f"{'='*40}")
        
        config = ExperimentConfig()
        config.dataset = 'mnist'
        config.model = 'cnn'
        config.num_clients = 50  # 减少客户端数量以加快测试
        config.num_rounds = 30   # 减少轮数以加快测试
        config.local_epochs = 3
        config.batch_size = 32
        config.learning_rate = 0.01
        config.iid = False
        config.alpha = alpha     # 变化的参数
        config.client_fraction = 0.2
        
        # SWIM参数
        config.temperature = 0.5
        config.mu = 0.5
        config.kr = 0.5
        config.out_dim = 256
        
        config.algorithms = ['swim_appafl', 'cafl']
        config.save_results = True
        config.plot_results = False  # 不绘制单个图，最后统一分析
        config.seed = 42
        
        experiment = FederatedLearningComparison(config)
        experiment.run_experiment()
        
        # 保存结果
        results_summary[alpha] = experiment.analyze_results()
    
    # 打印参数敏感性分析结果
    print("\n" + "=" * 80)
    print("参数敏感性分析结果")
    print("=" * 80)
    print(f"{'Alpha':<8} {'SWIM-APPAFL准确率':<18} {'CAFL准确率':<12} {'最佳算法':<10}")
    print("-" * 60)
    
    for alpha, summary in results_summary.items():
        swim_acc = summary.get('swim_appafl', {}).get('final_accuracy', 0)
        cafl_acc = summary.get('cafl', {}).get('final_accuracy', 0)
        best_alg = 'SWIM-APPAFL' if swim_acc > cafl_acc else 'CAFL'
        
        print(f"{alpha:<8} {swim_acc:<18.4f} {cafl_acc:<12.4f} {best_alg:<10}")
    
    return results_summary


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MNIST数据集SWIM-APPAFL vs CAFL对比实验')
    parser.add_argument('--experiment', type=str, default='basic',
                       choices=['basic', 'extended', 'sensitivity'],
                       help='实验类型选择')
    parser.add_argument('--gpu', type=int, default=None, help='指定GPU设备ID')
    
    args = parser.parse_args()
    
    # 设置GPU
    if args.gpu is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(args.gpu)
        print(f"使用GPU: {args.gpu}")
    
    # 运行指定实验
    if args.experiment == 'basic':
        experiment = run_mnist_swim_cafl_comparison()
    elif args.experiment == 'extended':
        experiment = run_extended_comparison()
    elif args.experiment == 'sensitivity':
        results = run_parameter_sensitivity_test()
    else:
        print(f"未知实验类型: {args.experiment}")
        return
    
    print("\n" + "=" * 70)
    print("实验完成！")
    print("=" * 70)


if __name__ == '__main__':
    main()
