#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行联邦学习算法对比试验的脚本
包含多种预设的实验配置
"""

import os
import sys
import argparse
from federated_learning_comparison import FederatedLearningComparison, ExperimentConfig


def run_cifar10_comparison():
    """运行CIFAR-10数据集上的对比试验"""
    print("=" * 60)
    print("CIFAR-10数据集联邦学习算法对比试验")
    print("=" * 60)
    
    config = ExperimentConfig()
    
    # CIFAR-10实验配置
    config.dataset = 'cifar10'
    config.model = 'cnn'
    config.num_clients = 100
    config.num_rounds = 50  # 减少轮数以便快速测试
    config.local_epochs = 5
    config.batch_size = 32
    config.learning_rate = 0.01
    
    # 数据分布设置
    config.iid = False
    config.alpha = 0.2  # 非IID程度
    
    # 异步联邦学习参数
    config.client_fraction = 0.1
    config.stale_threshold = 4
    config.aggregation_threshold = 6
    
    # SWIM算法参数
    config.temperature = 0.5
    config.mu = 0.5
    config.kr = 0.5
    config.out_dim = 256
    
    # 实验控制
    config.algorithms = ['fedavg', 'appafl', 'swim_appafl', 'cafl']
    config.save_results = True
    config.plot_results = True
    config.seed = 42
    
    # 运行实验
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()
    
    return experiment


def run_mnist_comparison():
    """运行MNIST数据集上的对比试验"""
    print("=" * 60)
    print("MNIST数据集联邦学习算法对比试验")
    print("=" * 60)
    
    config = ExperimentConfig()
    
    # MNIST实验配置
    config.dataset = 'mnist'
    config.model = 'cnn'
    config.num_clients = 50  # MNIST使用较少客户端
    config.num_rounds = 30
    config.local_epochs = 3
    config.batch_size = 64
    config.learning_rate = 0.01
    
    # 数据分布设置
    config.iid = False
    config.alpha = 0.5  # MNIST使用稍高的alpha值
    
    # 异步联邦学习参数
    config.client_fraction = 0.2
    config.stale_threshold = 3
    config.aggregation_threshold = 5
    
    # SWIM算法参数
    config.temperature = 0.3
    config.mu = 0.5
    config.kr = 0.4
    config.out_dim = 128
    
    # 实验控制
    config.algorithms = ['fedavg', 'appafl', 'swim_appafl', 'cafl']
    config.save_results = True
    config.plot_results = True
    config.seed = 42
    
    # 运行实验
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()
    
    return experiment


def run_quick_test():
    """运行快速测试（用于验证代码正确性）"""
    print("=" * 60)
    print("快速测试 - 验证代码正确性")
    print("=" * 60)
    
    config = ExperimentConfig()
    
    # 快速测试配置
    config.dataset = 'cifar10'
    config.model = 'cnn'
    config.num_clients = 10  # 少量客户端
    config.num_rounds = 5   # 少量轮数
    config.local_epochs = 2
    config.batch_size = 32
    config.learning_rate = 0.01
    
    # 数据分布设置
    config.iid = True  # 使用IID简化测试
    config.alpha = 1.0
    
    # 异步联邦学习参数
    config.client_fraction = 0.5
    config.stale_threshold = 2
    config.aggregation_threshold = 3
    
    # SWIM算法参数
    config.temperature = 0.5
    config.mu = 0.5
    config.kr = 0.5
    config.out_dim = 128
    
    # 实验控制（只测试部分算法）
    config.algorithms = ['fedavg', 'appafl']
    config.save_results = True
    config.plot_results = True
    config.seed = 42
    
    # 运行实验
    experiment = FederatedLearningComparison(config)
    experiment.run_experiment()
    
    return experiment


def run_comprehensive_comparison():
    """运行全面的对比试验"""
    print("=" * 60)
    print("全面对比试验 - 所有算法和数据集")
    print("=" * 60)
    
    datasets = ['cifar10', 'mnist']
    results_summary = {}
    
    for dataset in datasets:
        print(f"\n{'='*40}")
        print(f"运行 {dataset.upper()} 数据集实验")
        print(f"{'='*40}")
        
        config = ExperimentConfig()
        
        # 根据数据集调整配置
        if dataset == 'cifar10':
            config.dataset = 'cifar10'
            config.num_clients = 100
            config.num_rounds = 50
            config.batch_size = 32
            config.alpha = 0.2
        elif dataset == 'mnist':
            config.dataset = 'mnist'
            config.num_clients = 50
            config.num_rounds = 30
            config.batch_size = 64
            config.alpha = 0.5
        
        # 通用配置
        config.model = 'cnn'
        config.local_epochs = 5
        config.learning_rate = 0.01
        config.iid = False
        config.client_fraction = 0.1
        config.algorithms = ['fedavg', 'appafl', 'swim_appafl', 'cafl']
        config.save_results = True
        config.plot_results = True
        config.seed = 42
        
        # 运行实验
        experiment = FederatedLearningComparison(config)
        experiment.run_experiment()
        
        # 保存结果摘要
        results_summary[dataset] = experiment.analyze_results()
    
    # 打印总体结果摘要
    print("\n" + "=" * 80)
    print("全面对比试验结果摘要")
    print("=" * 80)
    
    for dataset, summary in results_summary.items():
        print(f"\n{dataset.upper()} 数据集结果:")
        best_algorithm = max(summary.keys(), key=lambda x: summary[x]['final_accuracy'])
        print(f"  最佳算法: {best_algorithm.upper()}")
        print(f"  最高准确率: {summary[best_algorithm]['final_accuracy']:.4f}")
    
    return results_summary


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='联邦学习算法对比试验运行脚本')
    parser.add_argument('--experiment', type=str, default='quick_test',
                       choices=['quick_test', 'cifar10', 'mnist', 'comprehensive'],
                       help='选择要运行的实验类型')
    parser.add_argument('--gpu', type=int, default=None, help='指定GPU设备ID')
    
    args = parser.parse_args()
    
    # 设置GPU
    if args.gpu is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(args.gpu)
        print(f"使用GPU: {args.gpu}")
    
    # 运行指定的实验
    if args.experiment == 'quick_test':
        experiment = run_quick_test()
    elif args.experiment == 'cifar10':
        experiment = run_cifar10_comparison()
    elif args.experiment == 'mnist':
        experiment = run_mnist_comparison()
    elif args.experiment == 'comprehensive':
        results = run_comprehensive_comparison()
    else:
        print(f"未知实验类型: {args.experiment}")
        return
    
    print("\n" + "=" * 60)
    print("实验完成！")
    print("=" * 60)


if __name__ == '__main__':
    main()
